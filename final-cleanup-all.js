#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Funkcja do rekurencyjnego przeszukiwania plików
function findFiles(dir, extensions) {
  let results = [];
  const list = fs.readdirSync(dir);
  
  list.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat && stat.isDirectory()) {
      if (!['node_modules', '.next', '.git'].includes(file)) {
        results = results.concat(findFiles(filePath, extensions));
      }
    } else if (extensions.some(ext => file.endsWith(ext))) {
      results.push(filePath);
    }
  });
  
  return results;
}

// Funkcja do naprawy wszystkich problemów
function fixAllIssues(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  let changed = false;
  
  // 1. Usuń wszystkie rounded elements
  const roundedPatterns = [
    /rounded-\w+/g,
    /border-radius:\s*[^;]+;/g,
    /borderRadius:\s*['"][^'"]+['"]/g,
    /borderRadius:\s*\d+/g
  ];
  
  roundedPatterns.forEach(pattern => {
    if (pattern.test(content)) {
      content = content.replace(pattern, '');
      changed = true;
    }
  });
  
  // 2. Napraw hardcoded typography - dodaj komentarze TODO
  const typographyPatterns = [
    { pattern: /<h1\s+className="[^"]*text-\d+xl[^"]*"/g, replacement: (match) => match + ' /* TODO: Replace with HeroTitle */' },
    { pattern: /<h2\s+className="[^"]*text-\d+xl[^"]*"/g, replacement: (match) => match + ' /* TODO: Replace with SectionTitle */' },
    { pattern: /<h3\s+className="[^"]*text-\d+xl[^"]*"/g, replacement: (match) => match + ' /* TODO: Replace with CardTitle */' },
    { pattern: /<h4\s+className="[^"]*text-\d+xl[^"]*"/g, replacement: (match) => match + ' /* TODO: Replace with CardTitle */' },
    { pattern: /<h5\s+className="[^"]*text-\d+xl[^"]*"/g, replacement: (match) => match + ' /* TODO: Replace with CardTitle */' },
    { pattern: /<h6\s+className="[^"]*text-\d+xl[^"]*"/g, replacement: (match) => match + ' /* TODO: Replace with CardTitle */' }
  ];
  
  typographyPatterns.forEach(({ pattern, replacement }) => {
    if (pattern.test(content)) {
      content = content.replace(pattern, replacement);
      changed = true;
    }
  });
  
  // 3. Napraw font-serif na font-cormorant
  if (content.includes('font-serif')) {
    content = content.replace(/font-serif/g, 'font-cormorant');
    changed = true;
  }
  
  // 4. Napraw hardcoded spacing
  const spacingMappings = {
    'py-20': 'py-section',
    'py-24': 'py-section',
    'py-32': 'py-section-lg',
    'px-6': 'px-hero-padding',
    'px-8': 'px-hero-padding',
    'mb-8': 'mb-lg',
    'mb-6': 'mb-md',
    'gap-8': 'gap-lg',
    'gap-6': 'gap-md'
  };
  
  Object.entries(spacingMappings).forEach(([oldSpacing, newSpacing]) => {
    const regex = new RegExp(`\\b${oldSpacing.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`, 'g');
    if (regex.test(content)) {
      content = content.replace(regex, newSpacing);
      changed = true;
    }
  });
  
  // 5. Napraw legacy colors
  const colorMappings = {
    'bg-shell': 'bg-sanctuary',
    'bg-temple': 'bg-charcoal',
    'bg-rice': 'bg-whisper',
    'bg-mist': 'bg-sage',
    'text-temple': 'text-charcoal',
    'text-temple-gold': 'text-enterprise-brown',
    'border-temple': 'border-charcoal'
  };
  
  Object.entries(colorMappings).forEach(([oldColor, newColor]) => {
    if (content.includes(oldColor)) {
      content = content.replace(new RegExp(oldColor, 'g'), newColor);
      changed = true;
    }
  });
  
  if (changed) {
    fs.writeFileSync(filePath, content, 'utf8');
    return true;
  }
  
  return false;
}

// Główna funkcja
function main() {
  const srcDir = path.join(__dirname, 'src');
  const files = findFiles(srcDir, ['.jsx', '.tsx', '.js', '.ts', '.css']);
  
  console.log(`🔍 Found ${files.length} files to fix...`);
  
  let fixedCount = 0;
  files.forEach(file => {
    if (fixAllIssues(file)) {
      fixedCount++;
      console.log(`✅ Fixed: ${path.relative(__dirname, file)}`);
    }
  });
  
  console.log(`\n🎉 Fixed issues in ${fixedCount} files!`);
  console.log('✨ BAKASANA style consistency improved!');
}

main();
