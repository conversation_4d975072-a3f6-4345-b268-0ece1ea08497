/* =============================================
   🏛️ BAKASANA OPTIMIZED STYLES - MAIN ENTRY POINT
   Clean, organized, and maintainable CSS architecture
   ============================================= */

/* Import optimized component styles */
@import './hero.css';
@import './sections.css';
@import './modern-css.css';

/* Core variables and base styles */
:root {
  /* ===== COLOR PALETTE ===== */
  --sanctuary: #FDFCF8;
  --whisper: #F9F7F3;
  --rice: #FAF8F4;
  --linen: #F6F2E8;
  --pearl: #F8F5F0;
  --silk: #F4F0E8;
  
  --charcoal: #2A2724;
  --charcoal-light: #4A453F;
  --stone: #8B8680;
  --stone-light: #B5B0A8;
  --sage: #A8B5A8;
  --ash: #D2CDC6;
  
  --temple-gold: #B8935C;
  --enterprise-brown: #8B7355;
  --terra: #A0845C;
  --golden-amber: #D4AF37;
  
  --pure-white: #FFFFFF;
  --soft-black: #1A1816;
  
  /* ===== TYPOGRAPHY ===== */
  --font-primary: 'Cormorant Garamond', 'Crimson Text', serif;
  --font-secondary: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  
  /* ===== SPACING SYSTEM ===== */
  --container-max: 1200px;
  --section-padding: 5rem 0;
  --element-breathing: 8%;
  --card-internal: 2rem;
  --micro-spacing: 1rem;
  
  /* ===== SHADOWS ===== */
  --shadow-subtle: 0 2px 10px rgba(26, 24, 22, 0.04);
  --shadow-elevated: 0 8px 25px rgba(26, 24, 22, 0.08);
  --shadow-premium: 0 10px 40px rgba(0, 0, 0, 0.06);
  
  /* ===== TRANSITIONS ===== */
  --transition-base: 0.3s ease;
  --transition-smooth: 0.5s cubic-bezier(0.165, 0.84, 0.44, 1);
}

/* ===== GLOBAL RESET & BASE STYLES ===== */
*,
*::before,
*::after {
  box-sizing: border-box;
   /* BAKASANA RULE: Zero border-radius */
}

html {
  scroll-behavior: smooth;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  font-feature-settings: "kern" 1, "liga" 1, "calt" 1;
}

body {
  font-family: var(--font-secondary);
  font-weight: 300;
  line-height: 1.8;
  color: var(--charcoal);
  background: var(--sanctuary);
  overflow-x: hidden;
}

/* ===== ACCESSIBILITY ===== */
.skip-link {
  position: absolute;
  top: -40px;
  left: 8px;
  background: var(--charcoal);
  color: var(--sanctuary);
  padding: 8px 16px;
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  z-index: 1000;
  transition: top 0.3s ease;
}

.skip-link:focus {
  top: 8px;
  outline: 2px solid var(--temple-gold);
  outline-offset: 2px;
}

/* ===== FOCUS STATES ===== */
button:focus,
a:focus,
input:focus,
textarea:focus,
select:focus {
  outline: none;
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

/* ===== SELECTION STYLING ===== */
::selection {
  background-color: var(--enterprise-brown);
  color: white;
}

::-moz-selection {
  background-color: var(--enterprise-brown);
  color: white;
}

/* ===== CURSOR STYLING ===== */
* {
  cursor: default;
}

a, 
button, 
[role="button"], 
input[type="submit"],
input[type="button"] {
  cursor: pointer;
}

/* ===== TYPOGRAPHY UTILITIES ===== */
.font-primary {
  font-family: var(--font-primary);
}

.font-secondary {
  font-family: var(--font-secondary);
}

.font-cormorant {
  font-family: 'Cormorant Garamond', serif;
}

.font-inter {
  font-family: 'Inter', sans-serif;
}

/* ===== RESPONSIVE IMAGES ===== */
img {
  max-width: 100%;
  height: auto;
}

/* ===== FORM ELEMENTS ===== */
input,
textarea,
select {
  background: var(--sanctuary);
  border: 1px solid var(--stone-light);
  color: var(--charcoal);
  font-family: var(--font-secondary);
  padding: 0.75rem 1rem;
  transition: border-color 0.3s ease;
}

input:focus,
textarea:focus,
select:focus {
  border-color: var(--temple-gold);
  outline: none;
}

/* ===== BUTTON RESET ===== */
button {
  background: none;
  border: none;
  padding: 0;
  font: inherit;
  color: inherit;
  cursor: pointer;
}

/* ===== LINK RESET ===== */
a {
  color: inherit;
  text-decoration: none;
}

/* ===== UTILITY CLASSES ===== */
.visually-hidden {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.uppercase {
  text-transform: uppercase;
}

.lowercase {
  text-transform: lowercase;
}

.capitalize {
  text-transform: capitalize;
}

/* ===== MOTION PREFERENCES ===== */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* ===== PRINT STYLES ===== */
@media print {
  * {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }
  
  a,
  a:visited {
    text-decoration: underline;
  }
  
  img {
    page-break-inside: avoid;
  }
  
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    page-break-after: avoid;
  }
}

/* ===== CONTAINER QUERIES SUPPORT ===== */
@supports (container-type: inline-size) {
  .container-query {
    container-type: inline-size;
  }
}

/* ===== MODERN CSS FEATURES ===== */
@supports (backdrop-filter: blur(10px)) {
  .backdrop-blur {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
  }
}

@supports (display: grid) {
  .grid-fallback {
    display: grid;
  }
}

/* ===== DARK MODE SUPPORT ===== */
@media (prefers-color-scheme: dark) {
  :root {
    --sanctuary: #1A1816;
    --whisper: #2A2724;
    --charcoal: #FDFCF8;
    --charcoal-light: #F9F7F3;
    --stone: #B5B0A8;
    --sage: #A8B5A8;
  }
}

/* ===== HIGH CONTRAST MODE ===== */
@media (prefers-contrast: high) {
  :root {
    --temple-gold: #FFD700;
    --enterprise-brown: #8B4513;
    --terra: #CD853F;
  }
}

/* ===== PERFORMANCE OPTIMIZATIONS ===== */
.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

.contain-layout {
  contain: layout;
}

.contain-paint {
  contain: paint;
}

.contain-strict {
  contain: strict;
}