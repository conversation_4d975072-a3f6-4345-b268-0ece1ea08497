/**
 * 🎯 BAKASANA - ENHANCED BUTTON COMPONENT
 * 
 * Award-winning button with:
 * - Magnetic hover effects
 * - Ripple animations
 * - Shine effects
 * - Haptic feedback simulation
 * - Accessibility compliance
 * - Performance optimization
 * 
 * Usage:
 * <EnhancedButton variant="primary" size="large" magnetic>
 *   Click me
 * </EnhancedButton>
 */

'use client';

import React, { forwardRef } from 'react';
import {  UnifiedButton  } from '@/components/ui/UnifiedButton';
import {  useButtonInteractions, useReducedMotion  } from '@/components/ui/UnifiedButton';
import {  UnifiedButton  } from '@/components/ui/UnifiedButton';
import { cn } from '@/lib/utils';

const EnhancedButton = forwardRef(({ 
  children, 
  variant = 'primary', 
  size = 'medium', 
  magnetic = false,
  shine = true,
  ripple = true,
  className,
  disabled = false,
  loading = false,
  onClick,
  ...props 
}, ref) => {
  const buttonRef = useButtonInteractions();
  const prefersReducedMotion = useReducedMotion();
  
  // Variant styles
  const variants = {
    primary: {
      base: 'bg-charcoal-gold text-sanctuary border-charcoal-gold hover:bg-terra-amber hover:border-terra-amber',
      focus: 'focus:ring-temple-gold-gold focus:ring-opacity-50',
      active: 'active:bg-charcoal-gold active:scale-95'
    },
    secondary: {
      base: 'bg-transparent text-charcoal-gold border-charcoal-gold hover:bg-charcoal-gold hover:text-sanctuary',
      focus: 'focus:ring-temple-gold-gold focus:ring-opacity-50',
      active: 'active:bg-charcoal-gold active:text-sanctuary'
    },
    ghost: {
      base: 'bg-transparent text-charcoal border-transparent hover:bg-whisper hover:text-charcoal-gold',
      focus: 'focus:ring-temple-gold-gold focus:ring-opacity-50',
      active: 'active:bg-sanctuary'
    },
    danger: {
      base: 'bg-charcoal-gold/50 text-white border-charcoal-gold hover:bg-red-600 hover:border-red-600',
      focus: 'focus:ring-red-500 focus:ring-opacity-50',
      active: 'active:bg-charcoal-gold/50 active:scale-95'
    }
  };
  
  // Size styles
  const sizes = {
    small: 'px-container-sm py-2 text-sm',
    medium: 'px-hero-padding py-3 text-base',
    large: 'px-hero-padding py-4 text-lg',
    xlarge: 'px-12 py-5 text-xl'
  };
  
  const currentVariant = variants[variant] || variants.primary;
  const currentSize = sizes[size] || sizes.medium;
  
  const baseClasses = cn(
    // Base styling
    'relative inline-flex items-center justify-center',
    'font-medium tracking-wider',
    'border-2 border-solid',
    'transition-all duration-300 ease-out',
    'transform-gpu',
    'focus:outline-none focus:ring-2 focus:ring-offset-2',
    'disabled:opacity-50 disabled:cursor-not-allowed',
    'overflow-hidden',
    'will-change-transform',
    
    // Variant and size classes
    currentVariant.base,
    currentVariant.focus,
    currentSize,
    
    // Magnetic effect
    magnetic && !prefersReducedMotion && 'magnetic-element',
    
    // Additional effects
    !disabled && !prefersReducedMotion && [
      'hover:shadow-lg hover:shadow-charcoal-gold/25',
      'hover:-translate-y-0.5',
      'active:translate-y-0',
      currentVariant.active
    ],
    
    // Disabled state
    disabled && 'opacity-50 cursor-not-allowed pointer-events-none',
    
    // Custom className
    className
  );
  
  const handleClick = (e) => {
    if (disabled || loading) {
      e.preventDefault();
      return;
    }
    
    // Ripple effect
    if (ripple && !prefersReducedMotion) {
      const button = e.currentTarget;
      const rect = button.getBoundingClientRect();
      const size = Math.max(rect.width, rect.height);
      const x = e.clientX - rect.left - size / 2;
      const y = e.clientY - rect.top - size / 2;
      
      const rippleElement = document.createElement('div');
      rippleElement.className = 'ripple-effect';
      rippleElement.style.cssText = `
        position: absolute;
        width: ${size}px;
        height: ${size}px;
        left: ${x}px;
        top: ${y}px;
        background: rgba(255, 255, 255, 0.5);
        
        transform: scale(0);
        animation: ripple 0.6s ease-out;
        pointer-events: none;
      `;
      
      button.appendChild(rippleElement);
      
      setTimeout(() => {
        rippleElement.remove();
      }, 600);
    }
    
    // Haptic feedback simulation
    if (navigator.vibrate) {
      navigator.vibrate(50);
    }
    
    onClick?.(e);
  };
  
  return (
    <button
      ref={ref || buttonRef}
      className={baseClasses}
      onClick={handleClick}
      disabled={disabled || loading}
      {...props}
    >
      {/* Shine effect */}
      {shine && !prefersReducedMotion && (
        <div className="absolute inset-0 -translate-x-full transition-transform duration-700 ease-out group-hover:translate-x-full bg-gradient-to-r from-transparent via-white/20 to-transparent" />
      )}
      
      {/* Loading spinner */}
      {loading && (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="w-5 h-5 border-2 border-current border-t-transparent rectangular animate-spin" />
        </div>
      )}
      
      {/* Button content */}
      <span className={cn(
        'relative z-10 flex items-center justify-center gap-2',
        'transition-all duration-300',
        loading && 'opacity-0'
      )}>
        {children}
      </span>
      
      {/* Glow effect */}
      <div className="absolute inset-0  bg-gradient-to-r from-charcoal-gold/20 via-golden-amber/20 to-enterprise-brown/20 opacity-0 transition-opacity duration-300 group-hover:opacity-100" />
    </button>
  );
});

EnhancedButton.displayName = 'EnhancedButton';

export default EnhancedButton;

// Add required styles to document
if (typeof document !== 'undefined') {
  const style = document.createElement('style');
  style.textContent = `
    @keyframes ripple {
      0% {
        transform: scale(0);
        opacity: 1;
      }
      100% {
        transform: scale(1);
        opacity: 0;
      }
    }
    
    .magnetic-element {
      transition: transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    }
    
    @media (prefers-reduced-motion: reduce) {
      .magnetic-element,
      .ripple-effect {
        animation: none !important;
        transition: none !important;
      }
    }
  `;
  document.head.appendChild(style);
}