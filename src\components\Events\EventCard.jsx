'use client';

import React from 'react';
import Image from 'next/image';
import {  Icon  } from '@/components/ui/IconSystem';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { Badge } from "@/components/ui/badge";
import styles from './EventCard.module.css';

// Mapowanie nazw ikon na komponenty
const iconMap = {
  'heart': Heart,
  'droplet': Droplet,
  'map-pin': MapPin,
  'palette': Palette,
  'sunrise': Sunrise
};

const EventCard = React.memo(({ event }) => {
  const IconComponent = iconMap[event.iconName] || Heart;
  
  return (
    <motion.div 
      className={styles.eventCard}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Link href={`/wydarzenia/${event.id}`} className={styles.eventLink}>
        <div className={`${styles.imageContainer} relative`}>
          <div className="relative w-full h-full">
            <Image
              src={event.imageUrl}
              alt={event.title}
              fill
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              className={styles.eventImage}
              loading="lazy"
              quality={75}
            />
          </div>
        </div>
        <div className={styles.eventContent}>
          <div className={styles.eventHeader}>
            <IconComponent className={styles.eventIcon} />
            <h3 className={styles.eventTitle}>{event.title}</h3>
          </div>
          <p className={styles.eventDescription}>{event.description}</p>
          <div className={styles.eventMeta}>
            <span className={styles.metaItem}>
              <Calendar size={14} /> {new Date(event.date).toLocaleDateString('pl-PL')}
            </span>
            <span className={styles.metaItem}>
              <MapPin size={14} /> {event.location}
            </span>
          </div>
          <div className={styles.tagsContainer}>
            {event.tags?.map(tag => (
              <Badge key={tag} variant="secondary" className={styles.tagBadge}>{tag}</Badge>
            ))}
          </div>
        </div>
      </Link>
    </motion.div>
  );
});

EventCard.displayName = 'EventCard';

export default EventCard; 