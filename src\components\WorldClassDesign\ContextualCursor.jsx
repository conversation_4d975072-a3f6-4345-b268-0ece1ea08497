'use client';

import React, { useEffect, useState } from 'react';

/**
 * 🎯 CONTEXTUAL CURSOR - TOP 1% DESIGN FEATURE
 * Różne kursory dla różnych elementów (grab, zoom-in, etc.)
 * Inspirowane przez Apple, Linear.app, najlepsze interactive sites
 */
const ContextualCursor = ({ 
  enabled = true,
  showCustomCursor = false,
  className = '',
  ...props 
}) => {
  const [cursorPosition, setCursorPosition] = useState({ x: 0, y: 0 });
  const [cursorState, setCursorState] = useState('default');
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    if (!enabled) return;

    const handleMouseMove = (e) => {
      setCursorPosition({ x: e.clientX, y: e.clientY });
      setIsVisible(true);
    };

    const handleMouseLeave = () => {
      setIsVisible(false);
    };

    const handleMouseOver = (e) => {
      const target = e.target;
      const computedStyle = window.getComputedStyle(target);
      const cursor = computedStyle.cursor;

      // Set contextual cursor based on element type and attributes
      if (target.tagName === 'BUTTON' || target.tagName === 'A') {
        setCursorState('pointer');
      } else if (target.draggable || target.dataset.draggable) {
        setCursorState('grab');
      } else if (target.dataset.zoomable || target.classList.contains('zoomable')) {
        setCursorState('zoom-in');
      } else if (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA') {
        setCursorState('text');
      } else if (target.dataset.resizable) {
        setCursorState('resize');
      } else if (target.dataset.move) {
        setCursorState('move');
      } else if (target.dataset.copy) {
        setCursorState('copy');
      } else if (target.dataset.crosshair) {
        setCursorState('crosshair');
      } else if (target.dataset.help) {
        setCursorState('help');
      } else if (target.dataset.wait) {
        setCursorState('wait');
      } else {
        setCursorState('default');
      }
    };

    // Set cursor styles for different element types
    const setCursorStyles = () => {
      const style = document.createElement('style');
      style.textContent = `
        /* Magnetic hover areas */
        button, a, [role="button"] {
          cursor: pointer !important;
          transition: transform 0.2s ease;
        }
        
        button:hover, a:hover, [role="button"]:hover {
          transform: translateY(-1px);
        }
        
        /* Draggable elements */
        [draggable="true"], [data-draggable] {
          cursor: grab !important;
        }
        
        [draggable="true"]:active, [data-draggable]:active {
          cursor: grabbing !important;
        }
        
        /* Zoomable images */
        img[data-zoomable], .zoomable {
          cursor: zoom-in !important;
        }
        
        /* Interactive elements */
        [data-interactive] {
          cursor: pointer !important;
        }
        
        /* Custom cursor states */
        [data-cursor="grab"] { cursor: grab !important; }
        [data-cursor="grabbing"] { cursor: grabbing !important; }
        [data-cursor="zoom-in"] { cursor: zoom-in !important; }
        [data-cursor="zoom-out"] { cursor: zoom-out !important; }
        [data-cursor="move"] { cursor: move !important; }
        [data-cursor="copy"] { cursor: copy !important; }
        [data-cursor="crosshair"] { cursor: crosshair !important; }
        [data-cursor="help"] { cursor: help !important; }
        [data-cursor="wait"] { cursor: wait !important; }
        [data-cursor="text"] { cursor: text !important; }
        [data-cursor="vertical-text"] { cursor: vertical-text !important; }
        [data-cursor="alias"] { cursor: alias !important; }
        [data-cursor="progress"] { cursor: progress !important; }
        [data-cursor="no-drop"] { cursor: no-drop !important; }
        [data-cursor="not-allowed"] { cursor: not-allowed !important; }
        
        /* Resize cursors */
        [data-cursor="n-resize"] { cursor: n-resize !important; }
        [data-cursor="s-resize"] { cursor: s-resize !important; }
        [data-cursor="e-resize"] { cursor: e-resize !important; }
        [data-cursor="w-resize"] { cursor: w-resize !important; }
        [data-cursor="ne-resize"] { cursor: ne-resize !important; }
        [data-cursor="nw-resize"] { cursor: nw-resize !important; }
        [data-cursor="se-resize"] { cursor: se-resize !important; }
        [data-cursor="sw-resize"] { cursor: sw-resize !important; }
        [data-cursor="ew-resize"] { cursor: ew-resize !important; }
        [data-cursor="ns-resize"] { cursor: ns-resize !important; }
        [data-cursor="nesw-resize"] { cursor: nesw-resize !important; }
        [data-cursor="nwse-resize"] { cursor: nwse-resize !important; }
        
        /* Hide default cursor when using custom cursor */
        ${showCustomCursor ? `
          * {
            cursor: none !important;
          }
        ` : ''}
      `;
      document.head.appendChild(style);
      
      return () => {
        document.head.removeChild(style);
      };
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseleave', handleMouseLeave);
    document.addEventListener('mouseover', handleMouseOver);
    
    const cleanup = setCursorStyles();

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseleave', handleMouseLeave);
      document.removeEventListener('mouseover', handleMouseOver);
      cleanup();
    };
  }, [enabled, showCustomCursor]);

  const getCursorIcon = () => {
    switch (cursorState) {
      case 'pointer':
        return '👆';
      case 'grab':
        return '✋';
      case 'grabbing':
        return '✊';
      case 'zoom-in':
        return '🔍';
      case 'text':
        return '📝';
      case 'move':
        return '↔️';
      case 'copy':
        return '📋';
      case 'help':
        return '❓';
      case 'wait':
        return '⏳';
      default:
        return '→';
    }
  };

  if (!enabled || !showCustomCursor) return null;

  return (
    <div
      className={`contextual-cursor ${className}`}
      style={{
        position: 'fixed',
        top: cursorPosition.y,
        left: cursorPosition.x,
        width: '20px',
        height: '20px',
        pointerEvents: 'none',
        zIndex: 9999,
        transition: 'transform 0.1s ease-out',
        transform: 'translate(-50%, -50%)',
        opacity: isVisible ? 1 : 0,
        fontSize: '16px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        background: 'rgba(255, 255, 255, 0.9)',
        ,
        backdropFilter: 'blur(4px)',
        border: '1px solid rgba(124, 152, 133, 0.2)',
        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
      }}
      {...props}
    >
      {getCursorIcon()}
    </div>
  );
};

export default React.memo(ContextualCursor);