import { blogPosts } from '@/data/blogPosts'; // Dosto<PERSON>j <PERSON> w razie potrzeby

export async function generateMetadata({ params }) {
  const { slug } = await params;
  const post = blogPosts.find((p) => p.slug === slug);

  if (!post) {
    // Możesz zwrócić domyślne metadane lub obsłużyć błąd
    return {
      title: 'Nie znaleziono posta',
      description: 'Szukany post nie został znaleziony.',
    };
  }

  // Zwróć dynamiczne metadane dla znalezionego posta
  return {
    title: `${post.title} | Mój Blog Podróżniczy`, // Dynamiczny tytuł
    description: post.description || post.content.substring(0, 160), // Użyj opisu lub początku treści
    // Możesz dodać więcej metadanych, np. openGraph
    openGraph: {
      title: `${post.title} | Mój Blog Podróżniczy`,
      description: post.description || post.content.substring(0, 160),
      type: 'article',
      publishedTime: post.date,
      authors: [post.author],
      url: `/blog/${post.slug}`, // Upewnij się, że URL jest poprawny
      images: [
        {
          url: post.imageUrl, // Użyj obrazka posta dla Open Graph
          width: 800, // Podaj wymiary obrazka
          height: 450,
          alt: post.title,
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: `${post.title} | Mój Blog Podróżniczy`,
      description: post.description || post.content.substring(0, 160),
      images: [post.imageUrl], // Użyj obrazka posta dla Twittera
    },
  };
} 