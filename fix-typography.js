#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Mapowanie hardcoded typography na unified components
const typographyMappings = {
  // Generic font-serif → font-cormorant
  'font-serif': 'font-cormorant',
  
  // Hardcoded heading styles → Unified components (będziemy flagować do manual review)
  // Te wymagają manualnej zamiany na komponenty
};

// Funkcja do rekurencyjnego przeszukiwania plików
function findFiles(dir, extensions) {
  let results = [];
  const list = fs.readdirSync(dir);
  
  list.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat && stat.isDirectory()) {
      if (!['node_modules', '.next', '.git'].includes(file)) {
        results = results.concat(findFiles(filePath, extensions));
      }
    } else if (extensions.some(ext => file.endsWith(ext))) {
      results.push(filePath);
    }
  });
  
  return results;
}

// Funkcja do naprawy typography
function fixTypography(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  let changed = false;
  
  // Napraw font-serif → font-cormorant
  if (content.includes('font-serif')) {
    content = content.replace(/font-serif/g, 'font-cormorant');
    changed = true;
  }
  
  // Flaguj hardcoded heading styles do manual review
  const hardcodedHeadingPatterns = [
    /className="[^"]*text-\d+xl[^"]*"/g,
    /className="[^"]*text-lg[^"]*font-medium[^"]*"/g,
    /className="[^"]*text-xl[^"]*font-light[^"]*"/g,
    /className="[^"]*text-2xl[^"]*font-serif[^"]*"/g,
    /className="[^"]*text-3xl[^"]*font-serif[^"]*"/g,
    /className="[^"]*text-4xl[^"]*font-serif[^"]*"/g,
  ];
  
  let hasHardcodedHeadings = false;
  hardcodedHeadingPatterns.forEach(pattern => {
    if (pattern.test(content)) {
      hasHardcodedHeadings = true;
    }
  });
  
  if (changed) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`✅ Fixed typography in: ${filePath}`);
    if (hasHardcodedHeadings) {
      console.log(`   ⚠️  Manual review needed: Contains hardcoded heading styles`);
    }
    return true;
  } else if (hasHardcodedHeadings) {
    console.log(`⚠️  Manual review needed: ${filePath} - Contains hardcoded heading styles`);
    return false;
  }
  
  return false;
}

// Główna funkcja
function main() {
  const srcDir = path.join(__dirname, 'src');
  const files = findFiles(srcDir, ['.jsx', '.tsx', '.js', '.ts']);
  
  console.log(`🔍 Found ${files.length} files to check for typography issues...`);
  
  let fixedCount = 0;
  files.forEach(file => {
    if (fixTypography(file)) {
      fixedCount++;
    }
  });
  
  console.log(`\n🎉 Fixed typography in ${fixedCount} files!`);
  console.log('✨ All font-serif replaced with font-cormorant.');
  console.log('📝 Files with hardcoded headings flagged for manual review.');
}

main();