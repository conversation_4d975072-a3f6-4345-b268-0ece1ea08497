/* =============================================
   👆 BAKASANA TOUCH TARGET OPTIMIZATION
   WCAG 2.1 AA Compliant Touch Targets
   ============================================= */

/* ===== TOUCH TARGET STANDARDS ===== */
:root {
  /* WCAG 2.1 AA minimum touch target size */
  --touch-target-min: 44px;
  --touch-target-comfortable: 48px;
  --touch-target-large: 52px;
  --touch-target-xl: 56px;
  
  /* Touch target spacing */
  --touch-spacing-min: 8px;
  --touch-spacing-comfortable: 12px;
  --touch-spacing-large: 16px;
}

/* ===== BASE TOUCH TARGET CLASSES ===== */

/* Minimum compliant touch target */
.touch-target {
  min-width: var(--touch-target-min);
  min-height: var(--touch-target-min);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

/* Comfortable touch target */
.touch-target-comfortable {
  min-width: var(--touch-target-comfortable);
  min-height: var(--touch-target-comfortable);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

/* Large touch target */
.touch-target-large {
  min-width: var(--touch-target-large);
  min-height: var(--touch-target-large);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

/* Extra large touch target */
.touch-target-xl {
  min-width: var(--touch-target-xl);
  min-height: var(--touch-target-xl);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

/* ===== INTERACTIVE ELEMENT OPTIMIZATION ===== */

/* Buttons */
button,
.btn,
[role="button"] {
  min-height: var(--touch-target-min);
  min-width: var(--touch-target-min);
  padding: 0.75rem 1.5rem;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

/* Links */
a {
  min-height: var(--touch-target-min);
  display: inline-flex;
  align-items: center;
  padding: 0.5rem;
  margin: -0.5rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s ease;
}

a:hover,
a:focus {
  background-color: rgba(139, 115, 85, 0.1);
}

/* Form inputs */
input,
textarea,
select {
  min-height: var(--touch-target-min);
  padding: 0.75rem 1rem;
  font-size: 1rem;
  line-height: 1.5;
}

/* Checkboxes and radio buttons */
input[type="checkbox"],
input[type="radio"] {
  min-width: var(--touch-target-min);
  min-height: var(--touch-target-min);
  transform: scale(1.5);
  margin: 0.5rem;
}

/* ===== NAVIGATION TOUCH TARGETS ===== */

/* Desktop navigation links */
.nav-link {
  min-height: var(--touch-target-comfortable);
  padding: 0.75rem 1rem;
  display: inline-flex;
  align-items: center;
  margin: 0 0.25rem;
  border-radius: 0.25rem;
  transition: all 0.2s ease;
}

/* Mobile navigation links */
.mobile-nav-link {
  min-height: var(--touch-target-comfortable);
  padding: 1rem 1.5rem;
  display: flex;
  align-items: center;
  width: 100%;
  border-radius: 0.25rem;
  margin: 0.25rem 0;
  transition: all 0.2s ease;
}

/* Hamburger menu button */
.hamburger-button {
  min-width: var(--touch-target-comfortable);
  min-height: var(--touch-target-comfortable);
  padding: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.25rem;
  transition: all 0.2s ease;
}

/* ===== CARD TOUCH TARGETS ===== */

/* Clickable cards */
.card-clickable {
  min-height: var(--touch-target-min);
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 0.5rem;
}

.card-clickable:hover,
.card-clickable:focus {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* ===== SOCIAL MEDIA TOUCH TARGETS ===== */

/* Social media icons */
.social-icon {
  min-width: var(--touch-target-comfortable);
  min-height: var(--touch-target-comfortable);
  padding: 0.75rem;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  margin: 0.25rem;
  transition: all 0.2s ease;
}

/* WhatsApp floating button */
.whatsapp-float {
  min-width: var(--touch-target-xl);
  min-height: var(--touch-target-xl);
  padding: 1rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

/* ===== FOOTER TOUCH TARGETS ===== */

/* Footer links */
.footer-link {
  min-height: var(--touch-target-min);
  padding: 0.75rem 0.5rem;
  display: inline-flex;
  align-items: center;
  margin: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  transition: all 0.2s ease;
}

/* Footer contact buttons */
.footer-contact-button {
  min-height: var(--touch-target-comfortable);
  padding: 1rem 1.5rem;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  border-radius: 0.5rem;
  margin: 0.5rem;
  transition: all 0.2s ease;
}

/* ===== RESPONSIVE TOUCH TARGET ADJUSTMENTS ===== */

/* Mobile devices - larger touch targets */
@media (max-width: 768px) {
  :root {
    --touch-target-min: 48px;
    --touch-target-comfortable: 52px;
    --touch-target-large: 56px;
    --touch-target-xl: 60px;
  }
  
  /* Increase spacing between touch targets */
  .mobile-nav-link {
    margin: 0.5rem 0;
    padding: 1.25rem 1.5rem;
  }
  
  /* Larger social icons on mobile */
  .social-icon {
    min-width: var(--touch-target-large);
    min-height: var(--touch-target-large);
    padding: 1rem;
  }
  
  /* Larger footer links on mobile */
  .footer-link {
    padding: 1rem 0.75rem;
    margin: 0.5rem 0.25rem;
  }
}

/* Tablet devices - medium touch targets */
@media (min-width: 769px) and (max-width: 1024px) {
  :root {
    --touch-target-min: 46px;
    --touch-target-comfortable: 50px;
    --touch-target-large: 54px;
    --touch-target-xl: 58px;
  }
}

/* ===== ACCESSIBILITY ENHANCEMENTS ===== */

/* Focus indicators for touch targets */
.touch-target:focus,
.touch-target-comfortable:focus,
.touch-target-large:focus,
.touch-target-xl:focus,
button:focus,
a:focus,
input:focus,
textarea:focus,
select:focus {
  outline: 2px solid var(--enterprise-brown);
  outline-offset: 2px;
  box-shadow: 0 0 0 4px rgba(139, 115, 85, 0.2);
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .touch-target,
  .touch-target-comfortable,
  .touch-target-large,
  .touch-target-xl,
  button,
  a,
  input,
  textarea,
  select {
    border: 2px solid currentColor;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .touch-target,
  .touch-target-comfortable,
  .touch-target-large,
  .touch-target-xl,
  button,
  a,
  .card-clickable,
  .social-icon,
  .whatsapp-float {
    transition: none;
  }
}

/* ===== TOUCH TARGET SPACING UTILITIES ===== */

/* Spacing between touch targets */
.touch-spacing {
  gap: var(--touch-spacing-min);
}

.touch-spacing-comfortable {
  gap: var(--touch-spacing-comfortable);
}

.touch-spacing-large {
  gap: var(--touch-spacing-large);
}

/* Margin utilities for touch targets */
.touch-margin {
  margin: var(--touch-spacing-min);
}

.touch-margin-comfortable {
  margin: var(--touch-spacing-comfortable);
}

.touch-margin-large {
  margin: var(--touch-spacing-large);
}

/* ===== PRINT STYLES ===== */
@media print {
  .touch-target,
  .touch-target-comfortable,
  .touch-target-large,
  .touch-target-xl {
    min-width: auto;
    min-height: auto;
    padding: 0.25rem 0.5rem;
  }
}

/* ===== ACCESSIBILITY ENHANCEMENTS ===== */

/* Skip links */
.skip-links {
  position: absolute;
  top: -100px;
  left: 0;
  z-index: 9999;
}

.skip-link {
  position: absolute;
  top: -100px;
  left: 0;
  background: var(--enterprise-brown);
  color: var(--sanctuary);
  padding: 0.75rem 1rem;
  text-decoration: none;
  font-weight: 500;
  border-radius: 0 0 0.25rem 0;
  transition: top 0.3s ease;
  min-height: var(--touch-target-min);
  display: flex;
  align-items: center;
}

.skip-link:focus {
  top: 0;
  outline: 2px solid var(--sanctuary);
  outline-offset: 2px;
}

/* Screen reader only content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* High contrast mode */
.high-contrast {
  --charcoal: #000000;
  --sanctuary: #ffffff;
  --enterprise-brown: #0066cc;
  --stone: #666666;
  --sage: #333333;
}

.high-contrast * {
  text-shadow: none !important;
  box-shadow: 0 0 0 1px currentColor !important;
}

.high-contrast img {
  filter: contrast(1.2) brightness(1.1);
}

/* Reduced motion */
.reduce-motion *,
.reduce-motion *::before,
.reduce-motion *::after {
  animation-duration: 0.01ms !important;
  animation-iteration-count: 1 !important;
  transition-duration: 0.01ms !important;
  scroll-behavior: auto !important;
}

/* Focus visible enhancement */
.focus-visible-enabled *:focus-visible {
  outline: 3px solid var(--enterprise-brown);
  outline-offset: 2px;
  box-shadow: 0 0 0 6px rgba(139, 115, 85, 0.2);
}

/* Enhanced focus indicators for interactive elements */
.focus-visible-enabled button:focus-visible,
.focus-visible-enabled a:focus-visible,
.focus-visible-enabled input:focus-visible,
.focus-visible-enabled textarea:focus-visible,
.focus-visible-enabled select:focus-visible {
  outline: 3px solid var(--enterprise-brown);
  outline-offset: 2px;
  box-shadow: 0 0 0 6px rgba(139, 115, 85, 0.2);
  position: relative;
  z-index: 10;
}

/* Keyboard navigation indicators */
.keyboard-navigation-active {
  outline: 2px dashed var(--enterprise-brown);
  outline-offset: 4px;
}

/* ARIA live regions */
[aria-live] {
  position: absolute;
  left: -10000px;
  width: 1px;
  height: 1px;
  overflow: hidden;
}

/* Enhanced button states for accessibility */
button[aria-pressed="true"] {
  background-color: var(--enterprise-brown);
  color: var(--sanctuary);
}

button[aria-expanded="true"] {
  background-color: var(--stone);
}

/* Enhanced form accessibility */
input:invalid,
textarea:invalid,
select:invalid {
  border: 2px solid #e74c3c;
  box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.2);
}

input:valid,
textarea:valid,
select:valid {
  border: 2px solid #27ae60;
  box-shadow: 0 0 0 3px rgba(39, 174, 96, 0.2);
}

/* Required field indicators */
[required]::after {
  content: " *";
  color: #e74c3c;
  font-weight: bold;
}

/* Error message styling */
.error-message {
  color: #e74c3c;
  font-size: 0.875rem;
  margin-top: 0.25rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.error-message::before {
  content: "⚠";
  font-weight: bold;
}

/* Success message styling */
.success-message {
  color: #27ae60;
  font-size: 0.875rem;
  margin-top: 0.25rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.success-message::before {
  content: "✓";
  font-weight: bold;
}
