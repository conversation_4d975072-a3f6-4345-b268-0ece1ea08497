'use client';

import { useState, useEffect } from 'react';
import {  UnifiedButton  } from '@/components/ui/UnifiedButton';
import {  motion, AnimatePresence  } from '@/components/ui/UnifiedButton';
import {  UnifiedButton  } from '@/components/ui/UnifiedButton';
import { ScrollReveal } from './ScrollReveal';

export default function NewsletterSignup({ 
  variant = 'default', 
  className = '',
  showLeadMagnet = true 
}) {
  const [email, setEmail] = useState('');
  const [status, setStatus] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!email || !email.includes('@')) {
      setStatus('❌ Podaj prawidłowy adres email');
      setTimeout(() => setStatus(''), 3000);
      return;
    }

    setIsSubmitting(true);
    setStatus('Zapisywanie...');

    try {
      // ConvertKit API integration
      const response = await fetch('/api/newsletter', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          tags: ['bali-guide-download', 'website-signup'],
          source: 'website'
        }),
      });

      const result = await response.json();

      if (result.success) {
        setShowSuccess(true);
        setEmail('');
        setStatus('');
        
        // Trigger download after successful signup
        if (showLeadMagnet) {
          setTimeout(() => {
            window.open('/downloads/przewodnik-po-bali.pdf', '_blank');
          }, 1000);
        }
      } else {
        throw new Error(result.error || 'Błąd zapisu');
      }
    } catch (error) {
      console.error('Newsletter signup error:', error);
      setStatus('❌ Wystąpił błąd. Spróbuj ponownie.');
    } finally {
      setIsSubmitting(false);
      setTimeout(() => setStatus(''), 5000);
    }
  };

  if (variant === 'popup') {
    return (
      <AnimatePresence>
        {!showSuccess && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
          >
            <motion.div
              initial={{ y: 50 }}
              animate={{ y: 0 }}
              className="bg-white rectangular p-8 max-w-md w-full shadow-2xl"
            >
              <div className="text-center mb-md">
                <div className="text-4xl mb-sm /* TODO: Replace with HeroTitle */">🏝️</div>
                <h3 className="text-2xl font-cormorant text-enterprise-brown mb-2 /* TODO: Replace with SectionTitle */" /* TODO: Replace with CardTitle */>
                  Darmowy Przewodnik po Bali
                </h3>
                <p className="text-charcoal-light text-sm">
                  Pobierz 20-stronicowy PDF z najlepszymi miejscami na Bali
                </p>
              </div>
              
              <form onSubmit={handleSubmit} className="space-y-sm">
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Twój adres email"
                  className="w-full px-container-sm py-3 border border-enterprise-brown/20 rectangular focus:outline-none focus:ring-2 focus:ring-temple-gold-gold/30"
                  required
                />
                
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full btn-unified-primary"
                >
                  {isSubmitting ? 'Wysyłanie...' : 'Pobierz Przewodnik'}
                </button>
                
                {status && (
                  <p className="text-sm text-center text-enterprise-brown">{status}</p>
                )}
              </form>
              
              <p className="text-xs text-charcoal-light/60 text-center mt-sm">
                Nie wysyłamy spamu. Możesz się wypisać w każdej chwili.
              </p>
            </motion.div>
          </motion.div>
        )}
        
        {showSuccess && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
          >
            <motion.div
              initial={{ y: 50 }}
              animate={{ y: 0 }}
              className="bg-white rectangular p-8 max-w-md w-full shadow-2xl text-center"
            >
              <div className="text-6xl mb-sm /* TODO: Replace with HeroTitle */">✅</div>
              <h3 className="text-2xl font-cormorant text-enterprise-brown mb-sm" /* TODO: Replace with CardTitle */>
                Dziękujemy!
              </h3>
              <p className="text-charcoal-light mb-md">
                Przewodnik zostanie pobrany automatycznie. 
                Sprawdź też swoją skrzynkę email!
              </p>
              <button
                onClick={() => setShowSuccess(false)}
                className="btn-unified-secondary"
              >
                Zamknij
              </button>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    );
  }

  if (variant === 'inline') {
    return (
      <ScrollReveal className={`bg-gradient-to-r from-charcoal/5 to-golden/5 rectangular p-8 ${className}`}>
        <div className="max-w-2xl mx-auto text-center">
          <div className="text-3xl mb-sm /* TODO: Replace with SectionTitle */">📧</div>
          <h3 className="text-2xl font-cormorant text-enterprise-brown mb-sm" /* TODO: Replace with CardTitle */>
            Newsletter Bali Yoga Journey
          </h3>
          <p className="text-charcoal-light mb-md">
            Otrzymuj inspiracje, tips o jodze i ekskluzywne oferty retreatów
          </p>
          
          <form onSubmit={handleSubmit} className="flex flex-col sm:flex-row gap-sm max-w-md mx-auto">
            <input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="Twój adres email"
              className="flex-1 px-container-sm py-3 border border-enterprise-brown/20 rectangular focus:outline-none focus:ring-2 focus:ring-temple-gold-gold/30"
              required
            />
            <button
              type="submit"
              disabled={isSubmitting}
              className="btn-unified-primary whitespace-nowrap"
            >
              {isSubmitting ? 'Zapisywanie...' : 'Zapisz się'}
            </button>
          </form>
          
          {status && (
            <p className="text-sm text-enterprise-brown mt-sm">{status}</p>
          )}
          
          {showLeadMagnet && (
            <p className="text-xs text-charcoal-light/60 mt-sm">
              🎁 Bonus: Darmowy przewodnik po Bali po zapisaniu się
            </p>
          )}
        </div>
      </ScrollReveal>
    );
  }

  // Default variant - hero section
  return (
    <div className={`text-center ${className}`}>
      <div className="bg-white/10 backdrop-blur-sm rectangular p-8 max-w-lg mx-auto">
        <div className="text-2xl mb-sm">🏝️</div>
        <h3 className="text-xl font-cormorant text-white mb-sm /* TODO: Replace with CardTitle */">
          Darmowy Przewodnik po Bali
        </h3>
        <p className="text-white/80 text-sm mb-md">
          20 stron najlepszych miejsc, restauracji i sekretnych plaż
        </p>
        
        <form onSubmit={handleSubmit} className="space-y-sm">
          <input
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            placeholder="Twój adres email"
            className="w-full px-container-sm py-3 bg-white/20 border border-white/30 rectangular text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/50"
            required
          />
          <button
            type="submit"
            disabled={isSubmitting}
            className="w-full bg-white text-enterprise-brown px-hero-padding py-3 rectangular font-medium hover:bg-white/90 transition-colors disabled:opacity-50"
          >
            {isSubmitting ? 'Wysyłanie...' : 'Pobierz Przewodnik'}
          </button>
        </form>
        
        {status && (
          <p className="text-sm text-white/90 mt-sm">{status}</p>
        )}
        
        <p className="text-xs text-white/60 mt-sm">
          Nie wysyłamy spamu. Możesz się wypisać w każdej chwili.
        </p>
      </div>
    </div>
  );
}

// Exit Intent Hook
export function useExitIntent(callback) {
  useEffect(() => {
    let hasTriggered = false;

    const handleMouseLeave = (e) => {
      if (e.clientY <= 0 && !hasTriggered) {
        hasTriggered = true;
        callback();
      }
    };

    document.addEventListener('mouseleave', handleMouseLeave);

    return () => {
      document.removeEventListener('mouseleave', handleMouseLeave);
    };
  }, [callback]);
}
