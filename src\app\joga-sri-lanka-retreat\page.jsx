import Image from 'next/image';
import Link from 'next/link';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Icon } from '@/components/ui/IconSystem';

export const metadata = {
  title: 'Joga Sri Lanka Retreat 2025 - Transformacyjne Podróże z Julią Jakubowicz',
  description: '🏆 Najlepsze retreaty jogi na Sri Lanka 2025 z certyfikowaną instruktorką Julią J<PERSON>icz. Sigiriya, Kandy, Ella, ayurveda, perła Oceanu Indyjskiego. 4.9/5 ⭐ Rezerwuj!',
  keywords: 'joga sri lanka, retreat sri lanka, sigiriya yoga, kandy medyta<PERSON>, ella plant<PERSON>, ayurveda sri lanka, julia j<PERSON><PERSON>, transformacyjne podróże',
  openGraph: {
    title: 'Joga Sri Lanka Retreat 2025 - Transformacyjne Podróże | BAKASANA',
    description: '🏆 Najlepsze retreaty jogi na Sri Lanka z Juli<PERSON> Jakubowicz. Sigiriya, Kandy, Ella, ayurveda. 4.9/5 ⭐ Rezerwuj teraz!',
    images: ['/images/og/joga-sri-lanka-retreat-2025.jpg'],
  },
  alternates: {
    canonical: 'https://bakasana-travel.blog/joga-sri-lanka-retreat',
  },
};

const JogaSriLankaRetreat = () => {
  const features = [
    {
      icon: <Icon name="mountain" size="md" color="accent" />,
      title: "Sigiriya Lwia Skała",
      description: "Sunrise yoga na 8th wonder of the world"
    },
    {
      icon: <Icon name="heart" size="md" color="accent" />,
      title: "Ayurveda Authentic",
      description: "Tradycyjne terapie i masaże ayurvedyjskie"
    },
    {
      icon: <Icon name="users" size="md" color="accent" />,
      title: "Małe grupy",
      description: "Maksymalnie 10 osób - intimate experience"
    },
    {
      icon: <Icon name="award" size="md" color="accent" />,
      title: "Doświadczona instruktorka",
      description: "Julia Jakubowicz - ekspert od Sri Lanka"
    }
  ];

  const itinerary = [
    {
      day: "Dzień 1-2",
      location: "Colombo → Sigiriya",
      activities: "Przyłot, aklimatyzacja, evening yoga"
    },
    {
      day: "Dzień 3-4", 
      location: "Sigiriya - Lwia Skała",
      activities: "Sunrise yoga, wspinaczka, ancient ruins"
    },
    {
      day: "Dzień 5-6",
      location: "Kandy - Góry",
      activities: "Mountain yoga, Temple of Tooth, meditation"
    },
    {
      day: "Dzień 7-8",
      location: "Ella - Plantacje",
      activities: "Tea plantation yoga, Nine Arch Bridge"
    },
    {
      day: "Dzień 9-10",
      location: "Południowe plaże",
      activities: "Beach yoga, ayurveda treatments, relax"
    }
  ];

  const included = [
    "Zakwaterowanie w boutique hotels",
    "Wszystkie posiłki (local cuisine)",
    "Daily joga i medytacja",
    "Autentyczna ayurveda",
    "Transport prywatny klimatyzowany",
    "Przewodnik lokalny",
    "Entrance fees wszystkie atrakcje",
    "Certyfikat Sri Lanka Yoga Experience"
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative h-screen flex items-center justify-center bg-gradient-to-br from-charcoal/10 to-golden/10">
        <div className="absolute inset-0 z-0">
          <Image
            src="/images/destinations/sigiriya-hero.webp"
            alt="Joga Sri Lanka - Sigiriya sunrise"
            fill
            className="object-cover opacity-30"
            priority
          />
        </div>
        
        <div className="relative z-10 text-center max-w-4xl mx-auto px-container-sm">
          <Badge className="mb-sm bg-charcoal/20 text-charcoal border-charcoal/30">
            Perła Oceanu Indyjskiego
          </Badge>
          
          <h1 className="text-4xl md:text-6xl font-bold text-charcoal mb-md /* TODO: Replace with HeroTitle */ /* TODO: Replace with HeroTitle */" /* TODO: Replace with HeroTitle */>
            Joga Sri Lanka <br />
            <span className="text-charcoal">Retreat 2025</span>
          </h1>
          
          <p className="text-xl text-wood mb-lg max-w-2xl mx-auto /* TODO: Replace with CardTitle */">
            Transformacyjne podróże z Julią Jakubowicz. Sigiriya, Kandy, Ella, 
            autentyczna ayurveda, duchowe doświadczenia. 10 dni odkrywania siebie.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-sm justify-center">
            <Button 
              size="lg" 
              className="bg-charcoal hover:bg-charcoal/90 text-lg px-hero-padding py-6"
              asChild
            >
              <Link href="/rezerwacja">
                Zarezerwuj Sri Lanka
                <ArrowRight className="w-5 h-5 ml-2" />
              </Link>
            </Button>
            
            <Button 
              size="lg" 
              variant="outline" 
              className="border-charcoal text-charcoal hover:bg-charcoal/10 text-lg px-hero-padding py-6"
              asChild
            >
              <Link href="/program?destination=srilanka">
                Zobacz Program
              </Link>
            </Button>
          </div>
          
          <div className="mt-lg flex items-center justify-center gap-md text-wood">
            <div className="flex items-center gap-2">
              <Icon name="clock" size="md" color="accent" />
              <span>10 dni</span>
            </div>
            <div className="flex items-center gap-2">
              <Icon name="users" size="md" color="accent" />
              <span>Max 10 osób</span>
            </div>
            <div className="flex items-center gap-2">
              <Icon name="star" size="md" className="text-terra fill-golden" />
              <span>4.9/5</span>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-section-md bg-sanctuary">
        <div className="max-w-7xl mx-auto px-container-sm">
          <div className="text-center mb-xl">
            <h2 className="text-3xl font-bold text-charcoal mb-sm /* TODO: Replace with SectionTitle */" /* TODO: Replace with SectionTitle */>
              Dlaczego Sri Lanka to idealne miejsce na retreat jogi?
            </h2>
            <p className="text-wood max-w-2xl mx-auto">
              Odkryj magię perły Oceanu Indyjskiego. Starożytne świątynie, 
              ayurveda, góry, plantacje herbaty i duchowa atmosfera.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-md">
            {features.map((feature, index) => (
              <Card key={index} className="bg-white/80 backdrop-blur-sm border-charcoal/20">
                <CardContent className="p-6 text-center">
                  <div className="w-12 h-12 bg-charcoal/10 flex items-center justify-center mx-auto mb-sm">
                    {feature.icon}
                  </div>
                  <h3 className="font-semibold text-charcoal mb-2">{feature.title}</h3>
                  <p className="text-sm text-wood">{feature.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Itinerary Section */}
      <section className="py-section-md">
        <div className="max-w-7xl mx-auto px-container-sm">
          <div className="text-center mb-xl">
            <h2 className="text-3xl font-bold text-charcoal mb-sm" /* TODO: Replace with SectionTitle */>
              10-dniowa podróż przez Sri Lanka
            </h2>
            <p className="text-wood max-w-2xl mx-auto">
              Szczegółowy program retreatu jogi obejmujący najpiękniejsze 
              i najbardziej duchowe miejsca Sri Lanka.
            </p>
          </div>
          
          <div className="space-y-md">
            {itinerary.map((item, index) => (
              <Card key={index} className="bg-white/80 backdrop-blur-sm border-charcoal/20">
                <CardContent className="p-6">
                  <div className="flex items-center gap-md">
                    <div className="w-16 h-16 bg-charcoal/10 flex items-center justify-center flex-shrink-0">
                      <span className="text-charcoal font-bold">{index + 1}</span>
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-sm mb-2">
                        <Badge variant="secondary" className="bg-charcoal/10 text-charcoal">
                          {item.day}
                        </Badge>
                        <div className="flex items-center gap-2">
                          <MapPin className="w-4 h-4 text-charcoal" />
                          <span className="font-semibold text-charcoal">{item.location}</span>
                        </div>
                      </div>
                      <p className="text-wood">{item.activities}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* What's Included */}
      <section className="py-section-md bg-sanctuary">
        <div className="max-w-7xl mx-auto px-container-sm">
          <div className="grid lg:grid-cols-2 gap-xl items-center">
            <div>
              <h2 className="text-3xl font-bold text-charcoal mb-md" /* TODO: Replace with SectionTitle */>
                Co zawiera retreat Sri Lanka?
              </h2>
              
              <div className="space-y-3 mb-lg">
                {included.map((item, index) => (
                  <div key={index} className="flex items-center gap-3">
                    <div className="w-6 h-6 bg-charcoal/10 flex items-center justify-center flex-shrink-0">
                      <Check className="w-4 h-4 text-charcoal" />
                    </div>
                    <span className="text-wood">{item}</span>
                  </div>
                ))}
              </div>
              
              <div className="bg-white/80 backdrop-blur-sm p-6 border border-charcoal/20">
                <h3 className="font-semibold text-charcoal mb-3">Cena retreatu:</h3>
                <div className="flex items-center gap-sm mb-3">
                  <span className="text-3xl font-bold text-charcoal">3800 PLN</span>
                  <Badge className="bg-terra/20 text-terra">
                    Early Bird -200 PLN
                  </Badge>
                </div>
                <p className="text-sm text-wood">
                  Cena za osobę, pokój podwójny. Dopłata single: 400 PLN
                </p>
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-sm">
              <div className="space-y-sm">
                <div className="relative aspect-square overflow-hidden">
                  <Image
                    src="/images/destinations/sigiriya-sunrise.webp"
                    alt="Sigiriya sunrise yoga"
                    fill
                    className="object-cover"
                  />
                </div>
                <div className="relative aspect-square overflow-hidden">
                  <Image
                    src="/images/destinations/kandy-temple.webp"
                    alt="Kandy Temple of Tooth"
                    fill
                    className="object-cover"
                  />
                </div>
              </div>
              <div className="space-y-sm mt-lg">
                <div className="relative aspect-square overflow-hidden">
                  <Image
                    src="/images/destinations/ella-train.webp"
                    alt="Ella train ride"
                    fill
                    className="object-cover"
                  />
                </div>
                <div className="relative aspect-square overflow-hidden">
                  <Image
                    src="/images/destinations/ayurveda-sri-lanka.webp"
                    alt="Ayurveda treatment Sri Lanka"
                    fill
                    className="object-cover"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-section-md bg-gradient-to-r from-charcoal/10 to-golden/10">
        <div className="max-w-4xl mx-auto px-container-sm text-center">
          <h2 className="text-3xl font-bold text-charcoal mb-sm" /* TODO: Replace with SectionTitle */>
            Odkryj magię Sri Lanka z jogi
          </h2>
          <p className="text-wood mb-lg max-w-2xl mx-auto">
            Dołącz do nas na wyjątkowym retreecie jogi na Sri Lanka. 
            Limitowane miejsca - tylko 10 osób!
          </p>
          
          <div className="flex flex-col sm:flex-row gap-sm justify-center mb-lg">
            <Button 
              size="lg" 
              className="bg-charcoal hover:bg-charcoal/90 text-lg px-hero-padding py-6"
              asChild
            >
              <Link href="/rezerwacja">
                Zarezerwuj Sri Lanka
                <ArrowRight className="w-5 h-5 ml-2" />
              </Link>
            </Button>
            
            <Button 
              size="lg" 
              variant="outline" 
              className="border-charcoal text-charcoal hover:bg-charcoal/10 text-lg px-hero-padding py-6"
              asChild
            >
              <Link href="/kontakt">
                Zadaj Pytanie
              </Link>
            </Button>
          </div>
          
          <div className="flex flex-col sm:flex-row items-center justify-center gap-md text-wood">
            <div className="flex items-center gap-2">
              <Phone className="w-4 h-4" />
              <span>+48 666 777 888</span>
            </div>
            <div className="flex items-center gap-2">
              <Mail className="w-4 h-4" />
              <span><EMAIL></span>
            </div>
            <div className="flex items-center gap-2">
              <Instagram className="w-4 h-4" />
              <span>@fly_with_bakasana</span>
            </div>
          </div>
        </div>
      </section>

      {/* Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "TouristTrip",
            "name": "Joga Sri Lanka Retreat 2025 - BAKASANA",
            "description": "Transformacyjne retreaty jogi na Sri Lanka z Julią Jakubowicz. Sigiriya, Kandy, Ella, ayurveda, perła Oceanu Indyjskiego.",
            "url": "https://bakasana-travel.blog/joga-sri-lanka-retreat",
            "image": "https://bakasana-travel.blog/images/destinations/sigiriya-hero.webp",
            "duration": "P10D",
            "startDate": "2025-03-01",
            "endDate": "2025-12-31",
            "offers": {
              "@type": "Offer",
              "price": "3800",
              "priceCurrency": "PLN",
              "availability": "https://schema.org/InStock",
              "url": "https://bakasana-travel.blog/rezerwacja"
            },
            "provider": {
              "@type": "TravelAgency",
              "name": "BAKASANA",
              "url": "https://bakasana-travel.blog"
            }
          })
        }}
      />
    </div>
  );
};

export default JogaSriLankaRetreat;