'use client';

import React, { useEffect, useState } from 'react';

/**
 * 📖 PROGRESS INDICATOR - TOP 1% DESIGN FEATURE
 * Cienka linia pokazująca postęp czytania
 * Time to read: Inteligentne wyliczanie czasu
 * Inspirowane przez Medium, Kinfolk, najlepsze editorial sites
 */
const ProgressIndicator = ({ 
  contentSelector = 'article, .content, main',
  showTimeToRead = true,
  wordsPerMinute = 200,
  className = '',
  position = 'top',
  ...props 
}) => {
  const [readingProgress, setReadingProgress] = useState(0);
  const [timeToRead, setTimeToRead] = useState(0);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const calculateTimeToRead = () => {
      const content = document.querySelector(contentSelector);
      if (!content) return;

      const text = content.textContent || content.innerText || '';
      const words = text.trim().split(/\s+/).length;
      const readingTime = Math.ceil(words / wordsPerMinute);
      setTimeToRead(readingTime);
    };

    const updateReadingProgress = () => {
      const content = document.querySelector(contentSelector);
      if (!content) return;

      const contentRect = content.getBoundingClientRect();
      const contentHeight = content.offsetHeight;
      const windowHeight = window.innerHeight;
      
      // Calculate how much of the content is visible
      const contentTop = contentRect.top;
      const contentBottom = contentRect.bottom;
      
      // Show indicator only when content is in view
      if (contentTop < windowHeight && contentBottom > 0) {
        setIsVisible(true);
        
        // Calculate progress
        const totalScrollableHeight = contentHeight - windowHeight;
        const scrolledHeight = Math.max(0, windowHeight - contentTop);
        const progress = Math.min(100, (scrolledHeight / totalScrollableHeight) * 100);
        
        setReadingProgress(isNaN(progress) ? 0 : progress);
      } else {
        setIsVisible(false);
      }
    };

    // Initial calculations
    calculateTimeToRead();
    updateReadingProgress();

    // Add scroll listener
    const handleScroll = () => {
      requestAnimationFrame(updateReadingProgress);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    window.addEventListener('resize', calculateTimeToRead);

    return () => {
      window.removeEventListener('scroll', handleScroll);
      window.removeEventListener('resize', calculateTimeToRead);
    };
  }, [contentSelector, wordsPerMinute]);

  const positionStyles = {
    top: {
      position: 'fixed',
      top: 0,
      left: 0,
      width: '100%',
      height: '2px',
      zIndex: 1000,
    },
    bottom: {
      position: 'fixed',
      bottom: 0,
      left: 0,
      width: '100%',
      height: '2px',
      zIndex: 1000,
    },
  };

  if (!isVisible) return null;

  return (
    <>
      {/* Progress bar */}
      <div
        className={`reading-progress ${className}`}
        style={{
          ...positionStyles[position],
          background: 'rgba(124, 152, 133, 0.1)',
          transition: 'opacity 0.3s ease',
        }}
        {...props}
      >
        <div
          style={{
            width: `${readingProgress}%`,
            height: '100%',
            background: 'linear-gradient(90deg, #7C9885, #A8C4B0)',
            transition: 'width 0.1s ease-out',
            boxShadow: readingProgress > 0 ? '0 0 10px rgba(124, 152, 133, 0.3)' : 'none',
          }}
        />
      </div>

      {/* Time to read indicator */}
      {showTimeToRead && (
        <div
          className="time-to-read"
          style={{
            position: 'fixed',
            top: position === 'top' ? '20px' : 'auto',
            bottom: position === 'bottom' ? '20px' : 'auto',
            right: '20px',
            background: 'rgba(255, 255, 255, 0.95)',
            backdropFilter: 'blur(10px)',
            padding: '8px 12px',
            ,
            fontSize: '12px',
            fontWeight: 300,
            color: '#7C9885',
            border: '1px solid rgba(124, 152, 133, 0.1)',
            zIndex: 999,
            opacity: isVisible ? 1 : 0,
            transform: isVisible ? 'translateY(0)' : 'translateY(10px)',
            transition: 'all 0.3s ease',
            pointerEvents: 'none',
            fontFamily: 'var(--font-sans)',
            letterSpacing: '0.02em',
          }}
        >
          <div style={{ display: 'flex', alignItems: 'center', gap: '6px' }}>
            <div
              style={{
                width: '4px',
                height: '4px',
                ,
                background: '#7C9885',
                animation: 'reading-pulse 2s ease-in-out infinite',
              }}
            />
            <span>{timeToRead} min read</span>
          </div>
        </div>
      )}

      <style jsx>{`
        @keyframes reading-pulse {
          0%, 100% { opacity: 0.5; transform: scale(1); }
          50% { opacity: 1; transform: scale(1.2); }
        }
        
        @media (max-width: 768px) {
          .time-to-read {
            font-size: 11px !important;
            padding: 6px 10px !important;
            right: 16px !important;
          }
        }
        
        @media (prefers-reduced-motion: reduce) {
          .reading-progress div {
            transition: none !important;
          }
          .time-to-read {
            transition: none !important;
          }
          .time-to-read div div {
            animation: none !important;
          }
        }
      `}</style>
    </>
  );
};

export default React.memo(ProgressIndicator);