'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import {  Icon  } from '@/components/ui/IconSystem';
import Link from 'next/link';
const DestinationCard = ({ 
  destination, 
  className = '',
  layout = 'standard',
  showPrice = true,
  showDuration = true,
  interactive = true
}) => {
  const [imageLoaded, setImageLoaded] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  const {
    id,
    name,
    country,
    description,
    shortDescription,
    image,
    price,
    priceNote,
    duration,
    nextDate,
    highlights,
    climate,
    bestTime,
    culture,
    slug,
    available = true,
    featured = false
  } = destination;

  const handleMouseEnter = () => {
    if (interactive) setIsHovered(true);
  };

  const handleMouseLeave = () => {
    if (interactive) setIsHovered(false);
  };

  const layoutClasses = {
    standard: 'h-96',
    compact: 'h-72',
    featured: 'h-[500px]',
    wide: 'h-80'
  };

  const textSizes = {
    standard: 'text-lg',
    compact: 'text-base',
    featured: 'text-xl',
    wide: 'text-lg'
  };

  return (
    <article
      className={`
        relative overflow-hidden bg-white group cursor-pointer
        ${layoutClasses[layout]}
        ${interactive ? 'transition-all duration-500 hover:shadow-2xl' : ''}
        ${className}
      `}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <Link 
        href={slug ? `/destynacje/${slug}` : `/destynacje/${id}`}
        className="block w-full h-full"
      >
        {/* Hero Image */}
        <div className="relative w-full h-2/3 overflow-hidden">
          <Image
            src={image}
            alt={`${name}, ${country} - yoga retreat destination`}
            fill
            className={`
              object-cover transition-all duration-700
              ${imageLoaded ? 'opacity-100' : 'opacity-0'}
              ${isHovered ? 'scale-105' : 'scale-100'}
            `}
            onLoad={() => setImageLoaded(true)}
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            priority={featured}
          />
          
          {/* Overlay */}
          <div className={`
            absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent
            ${isHovered ? 'opacity-80' : 'opacity-60'}
            transition-opacity duration-500
          `} />

          {/* Status Badge */}
          {featured && (
            <div className="absolute top-4 left-4 bg-charcoal-gold text-white px-3 py-1 text-sm font-medium">
              Polecane
            </div>
          )}

          {!available && (
            <div className="absolute top-4 right-4 bg-charcoal-gold/50 text-white px-3 py-1 text-sm font-medium">
              Niedostępne
            </div>
          )}

          {/* Quick Info Overlay */}
          <div className="absolute bottom-4 left-4 right-4 text-white">
            <div className="flex items-center gap-2 mb-2">
              <Icon name="map-pin" size="md" color="primary" />
              <span className="text-sm opacity-90">{country}</span>
            </div>
            <h3 className={`font-cormorant font-medium ${textSizes[layout]}`}>
              {name}
            </h3>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 h-1/3 flex flex-col justify-between">
          <div>
            <p className="text-stone text-sm line-clamp-2 mb-3 leading-relaxed">
              {shortDescription || description}
            </p>

            {/* Highlights */}
            {highlights && highlights.length > 0 && (
              <div className="flex flex-wrap gap-2 mb-3">
                {highlights.slice(0, 3).map((highlight, index) => (
                  <span
                    key={index}
                    className="text-xs bg-sanctuary text-stone px-2 py-1 rectangular"
                  >
                    {highlight}
                  </span>
                ))}
              </div>
            )}
          </div>

          {/* Bottom Info */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-sm">
              {showDuration && duration && (
                <div className="flex items-center gap-1 text-sm text-stone">
                  <Icon name="clock" size="md" color="primary" />
                  <span>{duration}</span>
                </div>
              )}
              
              {nextDate && (
                <div className="flex items-center gap-1 text-sm text-stone">
                  <CalendarDaysIcon className="h-4 w-4" />
                  <span>{nextDate}</span>
                </div>
              )}
            </div>

            {showPrice && price && (
              <div className="flex items-center gap-1 text-charcoal-gold font-medium">
                <CurrencyDollarIcon className="h-4 w-4" />
                <span>{price}</span>
                {priceNote && (
                  <span className="text-xs text-stone ml-1">
                    {priceNote}
                  </span>
                )}
              </div>
            )}
          </div>
        </div>
      </Link>

      {/* Hover Animation Elements */}
      {interactive && (
        <div className={`
          absolute inset-0 pointer-events-none
          ${isHovered ? 'opacity-100' : 'opacity-0'}
          transition-opacity duration-500
        `}>
          <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-charcoal-gold to-sage-green transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500" />
        </div>
      )}
    </article>
  );
};

export default DestinationCard;