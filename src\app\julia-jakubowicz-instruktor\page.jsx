import Image from 'next/image';
import Link from 'next/link';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Icon } from '@/components/ui/IconSystem';

export const metadata = {
  title: '<PERSON> - Certyfikowana Instruktorka Jogi | Ekspert Retreatów Bali & Sri Lanka',
  description: '🏆 <PERSON> - certyfikowana instruktorka jogi (200h YTT), fizjoterapeutka, ekspert retreatów Bali i Sri Lanka. 8 lat doświadczenia, 127 zadowolonych klientów. 4.9/5 ⭐',
  keywords: 'julia jak<PERSON><PERSON> joga, certyfikowana instruktorka jogi, ekspert retreatów, yoga teacher training, fizjoterapeutka joga, instruktor jogi bali, sri lanka yoga expert',
  openGraph: {
    title: '<PERSON> - Certyfikowana Instruktorka Jogi | BAKASANA',
    description: '🏆 <PERSON> - certyfikowana instruktorka jogi, ekspert retreatów Bali i Sri Lanka. 8 lat doświadczenia, 4.9/5 ⭐',
    images: ['/images/about/julia-jakubowicz-instruktor.jpg'],
  },
  alternates: {
    canonical: 'https://bakasana-travel.blog/julia-jakubowicz-instruktor',
  },
};

const JuliaJakubowiczInstruktor = () => {
  const achievements = [
    {
      icon: <Icon name="award" size="lg" color="primary" />,
      title: "200h YTT Certyfikat",
      description: "Yoga Teacher Training - międzynarodowy certyfikat",
      year: "2016"
    },
    {
      icon: <Icon name="heart" size="lg" color="primary" />,
      title: "Fizjoterapeutka",
      description: "Magister fizjoterapii - holistyczne podejście",
      year: "2014"
    },
    {
      icon: <Globe className="w-6 h-6 text-charcoal" />,
      title: "127 Zadowolonych Klientów",
      description: "Przeprowadzonych retreatów w Azji",
      year: "2017-2024"
    },
    {
      icon: <Target className="w-6 h-6 text-charcoal" />,
      title: "Ekspert Retreatów",
      description: "Specjalistka od Bali i Sri Lanka",
      year: "2017-teraz"
    }
  ];

  const specializations = [
    {
      name: "Hatha Yoga",
      description: "Tradycyjna praktyka dla wszystkich poziomów",
      icon: "🧘‍♀️"
    },
    {
      name: "Vinyasa Flow",
      description: "Dynamiczna sekwencja połączona z oddechem",
      icon: "🌊"
    },
    {
      name: "Medytacja",
      description: "Mindfulness i techniki kontemplacyjne",
      icon: "🧠"
    },
    {
      name: "Pranayama",
      description: "Techniki oddechowe i kontrola energii",
      icon: "💨"
    },
    {
      name: "Restorative Yoga",
      description: "Regenerująca praktyka z supportami",
      icon: "🛌"
    },
    {
      name: "Ayurveda",
      description: "Podstawy ayurvedyjskiej filozofii zdrowia",
      icon: "🌿"
    }
  ];

  const retreatStats = [
    { number: "127", label: "Zadowolonych klientów" },
    { number: "4.9", label: "Średnia ocena" },
    { number: "8", label: "Lat doświadczenia" },
    { number: "15", label: "Krajów zwiedzanych" }
  ];

  const testimonials = [
    {
      name: "Anna Kowalska",
      text: "Julia to nie tylko świetna instruktorka jogi, ale przede wszystkim mądra przewodniczka duchowa. Jej podejście do każdego uczestnika jest indywidualne i pełne ciepła.",
      rating: 5,
      retreat: "Bali Retreat 2024"
    },
    {
      name: "Marcin Nowak",
      text: "Profesjonalizm Julii w połączeniu z jej wiedzą medyczną jako fizjoterapeutki daje niesamowite rezultaty. Polecam każdemu!",
      rating: 5,
      retreat: "Sri Lanka Retreat 2023"
    },
    {
      name: "Katarzyna Wiśniewska",
      text: "Dzięki Julii odkryłam prawdziwą moc jogi. Jej retreaty to transformacyjne doświadczenie na wszystkich poziomach.",
      rating: 5,
      retreat: "Bali Retreat 2024"
    }
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative py-section bg-gradient-to-br from-charcoal/10 to-golden/10">
        <div className="max-w-7xl mx-auto px-container-sm">
          <div className="grid lg:grid-cols-2 gap-xl items-center">
            <div>
              <Badge className="mb-sm bg-charcoal/20 text-charcoal border-charcoal/30">
                Certyfikowana Instruktorka Jogi
              </Badge>
              
              <h1 className="text-4xl md:text-5xl font-bold text-charcoal mb-md /* TODO: Replace with HeroTitle */ /* TODO: Replace with HeroTitle */" /* TODO: Replace with HeroTitle */>
                Julia Jakubowicz
                <span className="block text-charcoal text-3xl md:text-4xl /* TODO: Replace with SectionTitle */">
                  Ekspert Retreatów Jogi
                </span>
              </h1>
              
              <p className="text-xl text-wood mb-lg max-w-xl /* TODO: Replace with CardTitle */">
                Certyfikowana instruktorka jogi (200h YTT) i fizjoterapeutka. 
                8 lat doświadczenia w prowadzeniu transformacyjnych retreatów 
                na Bali i Sri Lanka.
              </p>
              
              <div className="flex flex-wrap gap-sm mb-lg">
                <div className="flex items-center gap-2 bg-white/80 px-container-sm py-2">
                  <Icon name="star" size="sm" color="accent" />
                  <span className="font-semibold text-charcoal">4.9/5</span>
                  <span className="text-wood">127 opinii</span>
                </div>
                <div className="flex items-center gap-2 bg-white/80 px-container-sm py-2">
                  <Icon name="award" size="sm" color="primary" />
                  <span className="text-charcoal">200h YTT</span>
                </div>
                <div className="flex items-center gap-2 bg-white/80 px-container-sm py-2">
                  <Icon name="heart" size="sm" color="primary" />
                  <span className="text-charcoal">Fizjoterapeutka</span>
                </div>
              </div>
              
              <div className="flex flex-col sm:flex-row gap-sm">
                <Button 
                  size="lg" 
                  className="bg-charcoal hover:bg-charcoal/90 text-lg px-hero-padding py-6"
                  asChild
                >
                  <Link href="/rezerwacja">
                    Rezerwuj Retreat
                    <Icon name="arrow-right" size="md" color="primary" />
                  </Link>
                </Button>
                
                <Button 
                  size="lg" 
                  variant="outline" 
                  className="border-charcoal text-charcoal hover:bg-charcoal/10 text-lg px-hero-padding py-6"
                  asChild
                >
                  <Link href="/kontakt">
                    Skontaktuj się
                  </Link>
                </Button>
              </div>
            </div>
            
            <div className="relative">
              <div className="relative z-10 overflow-hidden">
                <Image
                  src="/images/about/julia-main.webp"
                  alt="Julia Jakubowicz - Certyfikowana Instruktorka Jogi"
                  width={600}
                  height={700}
                  className="object-cover w-full h-[600px]"
                  priority
                />
              </div>
              
              {/* Floating stats */}
              <div className="absolute top-8 -left-8 z-20 bg-white/90 backdrop-blur-sm p-4 shadow-lg">
                <div className="text-center">
                  <div className="text-2xl font-bold text-charcoal /* TODO: Replace with SectionTitle */">127</div>
                  <div className="text-sm text-wood">Zadowolonych klientów</div>
                </div>
              </div>
              
              <div className="absolute bottom-8 -right-8 z-20 bg-white/90 backdrop-blur-sm p-4 shadow-lg">
                <div className="text-center">
                  <div className="text-2xl font-bold text-charcoal">4.9★</div>
                  <div className="text-sm text-wood">Średnia ocena</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Achievements Section */}
      <section className="py-section-md bg-sanctuary">
        <div className="max-w-7xl mx-auto px-container-sm">
          <div className="text-center mb-xl">
            <h2 className="text-3xl font-bold text-charcoal mb-sm" /* TODO: Replace with SectionTitle */>
              Kwalifikacje i Osiągnięcia
            </h2>
            <p className="text-wood max-w-2xl mx-auto">
              Profesjonalne przygotowanie i wieloletnie doświadczenie 
              gwarantują najwyższą jakość prowadzonych retreatów.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-md">
            {achievements.map((achievement, index) => (
              <Card key={index} className="bg-white/80 backdrop-blur-sm border-charcoal/20">
                <CardContent className="p-6 text-center">
                  <div className="w-16 h-16 bg-charcoal/10 flex items-center justify-center mx-auto mb-sm">
                    {achievement.icon}
                  </div>
                  <Badge variant="secondary" className="mb-2 bg-charcoal/10 text-charcoal text-xs">
                    {achievement.year}
                  </Badge>
                  <h3 className="font-semibold text-charcoal mb-2">{achievement.title}</h3>
                  <p className="text-sm text-wood">{achievement.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Specializations */}
      <section className="py-section-md">
        <div className="max-w-7xl mx-auto px-container-sm">
          <div className="text-center mb-xl">
            <h2 className="text-3xl font-bold text-charcoal mb-sm" /* TODO: Replace with SectionTitle */>
              Specjalizacje Jogi
            </h2>
            <p className="text-wood max-w-2xl mx-auto">
              Szerokie spectrum praktyk jogi dostosowanych do indywidualnych 
              potrzeb i poziomu zaawansowania uczestników.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-md">
            {specializations.map((spec, index) => (
              <Card key={index} className="bg-white/80 backdrop-blur-sm border-charcoal/20 hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-center gap-sm mb-sm">
                    <div className="text-3xl">{spec.icon}</div>
                    <h3 className="font-semibold text-charcoal">{spec.name}</h3>
                  </div>
                  <p className="text-wood text-sm">{spec.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Statistics */}
      <section className="py-section-md bg-charcoal/5">
        <div className="max-w-7xl mx-auto px-container-sm">
          <div className="grid md:grid-cols-4 gap-lg">
            {retreatStats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="text-5xl font-bold text-charcoal mb-2">{stat.number}</div>
                <div className="text-wood font-medium">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Personal Story */}
      <section className="py-section-md">
        <div className="max-w-7xl mx-auto px-container-sm">
          <div className="grid lg:grid-cols-2 gap-xl items-center">
            <div>
              <h2 className="text-3xl font-bold text-charcoal mb-md" /* TODO: Replace with SectionTitle */>
                Moja Historia z Jogą
              </h2>
              
              <div className="space-y-md text-wood">
                <p>
                  Podróż z jogą rozpoczęłam w 2014 roku, kiedy to ukończyłam studia 
                  z fizjoterapii. Połączenie wiedzy medycznej z praktyką jogi 
                  otworzyło przede mną nowe perspektywy holistycznego podejścia 
                  do zdrowia i well-being.
                </p>
                
                <p>
                  W 2016 roku otrzymałam certyfikat 200h YTT (Yoga Teacher Training), 
                  który pozwolił mi profesjonalnie prowadzić zajęcia jogi. Rok później 
                  odbyłam pierwszą podróż na Bali, która totalnie zmieniła moje życie.
                </p>
                
                <p>
                  Od 2017 roku organizuję retreaty jogi w najpiękniejszych miejscach 
                  Azji. Przez 8 lat przeprowadziłam setki godzin praktyki z ludźmi 
                  z całego świata, pomagając im w transformacji i odkrywaniu 
                  własnego potencjału.
                </p>
                
                <p>
                  Obecnie specialyzuję się w retreatach na Bali i Sri Lanka, 
                  łącząc praktykę jogi z odkrywaniem kultury, duchowości i 
                  naturalnego piękna tych niezwykłych miejsc.
                </p>
              </div>
              
              <div className="mt-lg flex flex-wrap gap-sm">
                <Badge className="bg-charcoal/10 text-charcoal">
                  Fizjoterapeutka
                </Badge>
                <Badge className="bg-charcoal/10 text-charcoal">
                  200h YTT
                </Badge>
                <Badge className="bg-charcoal/10 text-charcoal">
                  Ekspert Bali
                </Badge>
                <Badge className="bg-charcoal/10 text-charcoal">
                  Sri Lanka Specialist
                </Badge>
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-sm">
              <div className="space-y-sm">
                <div className="relative aspect-square overflow-hidden">
                  <Image
                    src="/images/about/julia-teaching.webp"
                    alt="Julia Jakubowicz prowadzi zajęcia jogi"
                    fill
                    className="object-cover"
                  />
                </div>
                <div className="relative aspect-square overflow-hidden">
                  <Image
                    src="/images/about/julia-meditation.webp"
                    alt="Julia Jakubowicz podczas medytacji"
                    fill
                    className="object-cover"
                  />
                </div>
              </div>
              <div className="space-y-sm mt-lg">
                <div className="relative aspect-square overflow-hidden">
                  <Image
                    src="/images/about/julia-bali.webp"
                    alt="Julia Jakubowicz na Bali"
                    fill
                    className="object-cover"
                  />
                </div>
                <div className="relative aspect-square overflow-hidden">
                  <Image
                    src="/images/about/julia-srilanka.webp"
                    alt="Julia Jakubowicz na Sri Lanka"
                    fill
                    className="object-cover"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section className="py-section-md bg-sanctuary">
        <div className="max-w-7xl mx-auto px-container-sm">
          <div className="text-center mb-xl">
            <h2 className="text-3xl font-bold text-charcoal mb-sm" /* TODO: Replace with SectionTitle */>
              Co mówią uczestnicy o Julii?
            </h2>
            <p className="text-wood max-w-2xl mx-auto">
              Autentyczne opinie osób, które doświadczyły transformacji 
              pod opieką Julii podczas retreatów jogi.
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-lg">
            {testimonials.map((testimonial, index) => (
              <Card key={index} className="bg-white/80 backdrop-blur-sm border-charcoal/20">
                <CardContent className="p-6">
                  <div className="flex items-center gap-1 mb-sm">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star key={i} className="w-4 h-4 text-terra fill-golden" />
                    ))}
                  </div>
                  <p className="text-wood mb-md italic">"{testimonial.text}"</p>
                  <div className="border-t pt-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-semibold text-charcoal">{testimonial.name}</p>
                        <p className="text-sm text-wood">{testimonial.retreat}</p>
                      </div>
                      <Badge variant="secondary" className="bg-charcoal/10 text-charcoal text-xs">
                        Verified
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-section-md bg-gradient-to-r from-charcoal/10 to-golden/10">
        <div className="max-w-4xl mx-auto px-container-sm text-center">
          <h2 className="text-3xl font-bold text-charcoal mb-sm" /* TODO: Replace with SectionTitle */>
            Gotowy na transformację z Julią?
          </h2>
          <p className="text-wood mb-lg max-w-2xl mx-auto">
            Dołącz do grupy 127 zadowolonych uczestników, którzy doświadczyli 
            transformacji podczas retreatów z certyfikowaną instruktorką Julią Jakubowicz.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-sm justify-center mb-lg">
            <Button 
              size="lg" 
              className="bg-charcoal hover:bg-charcoal/90 text-lg px-hero-padding py-6"
              asChild
            >
              <Link href="/rezerwacja">
                Zarezerwuj Retreat
                <Icon name="arrow-right" size="md" color="primary" />
              </Link>
            </Button>
            
            <Button 
              size="lg" 
              variant="outline" 
              className="border-charcoal text-charcoal hover:bg-charcoal/10 text-lg px-hero-padding py-6"
              asChild
            >
              <Link href="/program">
                Zobacz Program
              </Link>
            </Button>
          </div>
          
          <div className="flex flex-col sm:flex-row items-center justify-center gap-md text-wood">
            <div className="flex items-center gap-2">
              <Icon name="phone" size="sm" color="primary" />
              <span>+48 666 777 888</span>
            </div>
            <div className="flex items-center gap-2">
              <Icon name="mail" size="sm" color="primary" />
              <span><EMAIL></span>
            </div>
            <div className="flex items-center gap-2">
              <Icon name="instagram" size="sm" color="primary" />
              <span>@fly_with_bakasana</span>
            </div>
          </div>
        </div>
      </section>

      {/* Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "Person",
            "name": "Julia Jakubowicz",
            "jobTitle": "Certyfikowana Instruktorka Jogi",
            "description": "Certyfikowana instruktorka jogi (200h YTT) i fizjoterapeutka specjalizująca się w retreatach jogi na Bali i Sri Lanka",
            "url": "https://bakasana-travel.blog/julia-jakubowicz-instruktor",
            "image": "https://bakasana-travel.blog/images/about/julia-jakubowicz-instruktor.jpg",
            "telephone": "+48666777888",
            "email": "<EMAIL>",
            "address": {
              "@type": "PostalAddress",
              "addressCountry": "PL"
            },
            "worksFor": {
              "@type": "Organization",
              "name": "BAKASANA",
              "url": "https://bakasana-travel.blog"
            },
            "hasCredential": [
              {
                "@type": "EducationalOccupationalCredential",
                "name": "200h Yoga Teacher Training",
                "credentialCategory": "certification"
              },
              {
                "@type": "EducationalOccupationalCredential", 
                "name": "Magister Fizjoterapii",
                "credentialCategory": "degree"
              }
            ],
            "knowsAbout": [
              "Hatha Yoga",
              "Vinyasa Yoga",
              "Meditation",
              "Pranayama",
              "Ayurveda",
              "Yoga Retreats",
              "Physiotherapy"
            ],
            "sameAs": [
              "https://www.instagram.com/fly_with_bakasana/",
              "https://www.facebook.com/bakasana.yoga"
            ],
            "aggregateRating": {
              "@type": "AggregateRating",
              "ratingValue": "4.9",
              "reviewCount": "127",
              "bestRating": "5"
            }
          })
        }}
      />
    </div>
  );
};

export default JuliaJakubowiczInstruktor;