#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Funkcja do rekurencyjnego przeszukiwania plików
function findFiles(dir, extensions) {
  let results = [];
  const list = fs.readdirSync(dir);
  
  list.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat && stat.isDirectory()) {
      if (!['node_modules', '.next', '.git'].includes(file)) {
        results = results.concat(findFiles(filePath, extensions));
      }
    } else if (extensions.some(ext => file.endsWith(ext))) {
      results.push(filePath);
    }
  });
  
  return results;
}

// Funkcja do dodawania importu UnifiedTypography jeśli nie istnieje
function ensureTypographyImport(content) {
  if (content.includes('UnifiedTypography')) {
    return content;
  }
  
  // Znajdź ostatni import
  const lines = content.split('\n');
  let lastImportIndex = -1;
  let hasReactImport = false;
  
  for (let i = 0; i < lines.length; i++) {
    if (lines[i].startsWith('import ')) {
      lastImportIndex = i;
      if (lines[i].includes('react')) {
        hasReactImport = true;
      }
    }
  }
  
  if (lastImportIndex === -1) {
    return content; // Brak importów
  }
  
  // Dodaj import UnifiedTypography
  const importLine = "\nimport { HeroTitle, SectionTitle, CardTitle, BodyText } from '@/components/ui/UnifiedTypography';";
  
  lines.splice(lastImportIndex + 1, 0, importLine);
  return lines.join('\n');
}

// Funkcja do naprawy hardcoded typography
function fixHardcodedTypography(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  let changed = false;
  
  // Mapowanie hardcoded headings na komponenty
  const headingMappings = [
    // Hero titles (h1 z dużymi rozmiarami)
    {
      pattern: /<h1\s+className="[^"]*text-4xl[^"]*"[^>]*>(.*?)<\/h1>/g,
      replacement: '<HeroTitle>$1</HeroTitle>'
    },
    {
      pattern: /<h1\s+className="[^"]*text-5xl[^"]*"[^>]*>(.*?)<\/h1>/g,
      replacement: '<HeroTitle>$1</HeroTitle>'
    },
    {
      pattern: /<h1\s+className="[^"]*text-6xl[^"]*"[^>]*>(.*?)<\/h1>/g,
      replacement: '<HeroTitle>$1</HeroTitle>'
    },
    
    // Section titles (h2, h3 z średnimi rozmiarami)
    {
      pattern: /<h2\s+className="[^"]*text-3xl[^"]*"[^>]*>(.*?)<\/h2>/g,
      replacement: '<SectionTitle>$1</SectionTitle>'
    },
    {
      pattern: /<h2\s+className="[^"]*text-2xl[^"]*"[^>]*>(.*?)<\/h2>/g,
      replacement: '<SectionTitle>$1</SectionTitle>'
    },
    {
      pattern: /<h3\s+className="[^"]*text-2xl[^"]*"[^>]*>(.*?)<\/h3>/g,
      replacement: '<SectionTitle level={3}>$1</SectionTitle>'
    },
    
    // Card titles (h3, h4 z małymi rozmiarami)
    {
      pattern: /<h3\s+className="[^"]*text-xl[^"]*"[^>]*>(.*?)<\/h3>/g,
      replacement: '<CardTitle>$1</CardTitle>'
    },
    {
      pattern: /<h4\s+className="[^"]*text-lg[^"]*"[^>]*>(.*?)<\/h4>/g,
      replacement: '<CardTitle level={4}>$1</CardTitle>'
    },
    {
      pattern: /<h3\s+className="[^"]*text-lg[^"]*font-medium[^"]*"[^>]*>(.*?)<\/h3>/g,
      replacement: '<CardTitle level={3}>$1</CardTitle>'
    },
    
    // Body text z font-light
    {
      pattern: /<p\s+className="[^"]*text-lg[^"]*font-light[^"]*"[^>]*>(.*?)<\/p>/g,
      replacement: '<BodyText size="lg" className="font-light">$1</BodyText>'
    }
  ];
  
  // Zastosuj wszystkie mapowania
  headingMappings.forEach(mapping => {
    if (mapping.pattern.test(content)) {
      content = content.replace(mapping.pattern, mapping.replacement);
      changed = true;
    }
  });
  
  // Dodaj import jeśli coś zostało zmienione
  if (changed) {
    content = ensureTypographyImport(content);
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`✅ Fixed hardcoded typography in: ${filePath}`);
    return true;
  }
  
  return false;
}

// Główna funkcja
function main() {
  const srcDir = path.join(__dirname, 'src');
  const files = findFiles(srcDir, ['.jsx', '.tsx']);
  
  console.log(`🔍 Found ${files.length} files to check for hardcoded typography...`);
  
  let fixedCount = 0;
  files.forEach(file => {
    if (fixHardcodedTypography(file)) {
      fixedCount++;
    }
  });
  
  console.log(`\n🎉 Fixed hardcoded typography in ${fixedCount} files!`);
  console.log('✨ All headings now use unified BAKASANA typography components.');
}

main();