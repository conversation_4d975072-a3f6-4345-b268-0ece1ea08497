import Image from 'next/image';
import Link from 'next/link';
import React from 'react';
import { notFound } from 'next/navigation';

import {  UnifiedButton  } from '@/components/ui/UnifiedButton';

import BlogWhatsAppCTA from '@/components/BlogWhatsAppCTA';
import Breadcrumbs from '@/components/Breadcrumbs';
import OptimizedIcon from '@/components/OptimizedIcon';
import PerformantWhatsApp from '@/components/PerformantWhatsApp';
import { generateMetadata } from './metadata';

import { blogPosts } from '@/data/blogPosts';
import { generateArticleSchema } from '@/lib/structuredData';

import { HeroTitle, SectionTitle, CardTitle, BodyText } from '@/components/ui/UnifiedTypography';

function formatDate(dateString) {
  const options = { year: 'numeric', month: 'long', day: 'numeric' };
  return new Date(dateString).toLocaleDateString('pl-PL', options);
}

export async function generateStaticParams() {
  return blogPosts.map((post) => ({
    slug: post.slug,
  }));
}

// Eksportuj funkcję generateMetadata z pliku metadata.js
export { generateMetadata };

export default async function PostPage({ params }) {
  try {
    const { slug } = await params;
    const post = blogPosts.find((p) => p.slug === slug);

    if (!post) {
      notFound();
    }

    const relatedPosts = post.tags
      ? blogPosts
          .filter(p => p.slug !== post.slug && p.tags?.some(tag => post.tags.includes(tag)))
          .slice(0, 3)
      : [];

    // Generuj structured data dla tego posta
    const structuredData = generateArticleSchema({
      title: post.title,
      excerpt: post.excerpt,
      image: post.imageUrl,
      date: post.date,
      slug: post.slug,
      dateModified: post.dateModified || post.date
    });

    return (
      <>
        {/* Structured Data */}
        {structuredData && (
          <script
            type="application/ld+json"
            dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
          />
        )}

        {/* Main Content */}
      <div className="relative bg-gradient-to-b from-shell/40 via-rice/60 to-whisper/30 min-h-screen">
        {/* Breadcrumbs & Navigation */}
        <div className="max-w-4xl mx-auto px-container-sm sm:px-hero-padding lg:px-hero-padding pt-8">
          <Breadcrumbs
            customItems={[
              { label: 'Strona główna', href: '/' },
              { label: 'Blog', href: '/blog' },
              { label: post.title, href: null }
            ]}
          />

          <Link
            href="/blog"
            className="inline-flex items-center gap-2 text-charcoal/70 hover:text-charcoal transition-colors duration-300 group mt-sm"
          >
            <OptimizedIcon name="ArrowLeft" className="w-4 h-4 group-hover:-translate-x-1 transition-transform duration-300" />
            <span className="text-sm font-medium">Wróć do bloga</span>
          </Link>
        </div>

        {/* Hero Image */}
        {post.imageUrl && (
          <div className="relative h-[60vh] overflow-hidden">
            <Image
              src={post.imageUrl}
              alt={post.imageAlt || post.title}
              fill
              priority
              className="object-cover"
              sizes="100vw"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-charcoal/60 via-temple/20 to-transparent"></div>

            {/* Category Badge */}
            <div className="absolute top-8 left-8">
              <span className="px-container-sm py-2 bg-charcoal/90 text-sanctuary text-sm font-medium uppercase tracking-wide backdrop-blur-sm border border-sanctuary/20">
                {post.category || 'Joga'}
              </span>
            </div>


          </div>
        )}

        {/* Article Content - Ultra Clean */}
        <div className="max-w-4xl mx-auto px-container-sm sm:px-hero-padding lg:px-hero-padding py-12">
          <article>
            {/* Article Header - Typography Only */}
            <header className="mb-xl">
              <h1 className="text-5xl md:text-6xl font-cormorant text-black leading-tight mb-lg /* TODO: Replace with HeroTitle */ /* TODO: Replace with HeroTitle */" /* TODO: Replace with HeroTitle */>
                {post.title}
              </h1>

              <div className="text-sm text-gray-600 mb-lg">
                <time dateTime={post.date} className="font-light">
                  {formatDate(post.date)}
                </time>
                <span className="mx-4">•</span>
                <span className="font-light">{post.author}</span>
                <span className="mx-4">•</span>
                <span className="font-light">{post.readTime || '5 minut'}</span>
              </div>
            </header>

            {/* Article Body */}
            <div className="prose prose-lg max-w-none p-8 prose-headings:text-charcoal prose-headings:font-cormorant prose-p:text-charcoal-light prose-p:leading-relaxed prose-a:text-charcoal prose-a:no-underline hover:prose-a:text-terra prose-strong:text-charcoal prose-blockquote:border-l-temple prose-blockquote:bg-charcoal/5 prose-blockquote:text-charcoal-light prose-ul:text-charcoal-light prose-ol:text-charcoal-light">
              <div dangerouslySetInnerHTML={{ __html: post.content }} />
            </div>

            {/* Blog WhatsApp CTA */}
            <BlogWhatsAppCTA 
              category={post.category}
              postTitle={post.title}
            />
          </article>

          {/* Related Posts */}
          {relatedPosts.length > 0 && (
            <section className="mt-2xl">
              <div className="text-center mb-lg">
                <SectionTitle>Powiązane Artykuły</SectionTitle>
                <p className="text-charcoal-light/80 text-sm">Może Cię również zainteresować</p>
                <div className="w-12 h-0.5 bg-charcoal/20 mx-auto mt-sm"></div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-md">
                {relatedPosts.map(relatedPost => (
                  <Link
                    href={`/blog/${relatedPost.slug}`}
                    key={relatedPost.slug}
                    className="group bg-silk/60 backdrop-blur-sm border border-charcoal/10 overflow-hidden hover:shadow-medium transition-all duration-300 hover:-translate-y-1"
                  >
                    {relatedPost.imageUrl && (
                      <div className="relative h-40 overflow-hidden">
                        <Image
                          src={relatedPost.imageUrl}
                          alt={relatedPost.title}
                          fill
                          className="object-cover group-hover:scale-105 transition-transform duration-300"
                          sizes="(max-width: 768px) 100vw, 33vw"
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-charcoal/20 to-transparent"></div>
                      </div>
                    )}
                    <div className="p-4">
                      <h3 className="font-cormorant text-charcoal text-lg leading-tight group-hover:text-terra transition-colors duration-300">
                        {relatedPost.title}
                      </h3>
                      <p className="text-charcoal-light/70 text-sm mt-2 line-clamp-2">
                        {relatedPost.excerpt}
                      </p>
                    </div>
                  </Link>
                ))}
              </div>
            </section>
          )}

          {/* Share & Back Navigation */}
          <div className="mt-2xl flex flex-col sm:flex-row items-center justify-between gap-sm p-6 bg-charcoal/5 backdrop-blur-sm">
            <div className="flex items-center gap-sm">
              <span className="text-charcoal font-medium text-sm">Podziel się artykułem:</span>
              <div className="flex items-center gap-2">
                <UnifiedButton variant="primary" size="md">
                  <OptimizedIcon name="Share2" className="w-4 h-4 text-charcoal" />
                </UnifiedButton>
                <UnifiedButton variant="primary" size="md">
                  <OptimizedIcon name="Heart" className="w-4 h-4 text-charcoal" />
                </UnifiedButton>
              </div>
            </div>

            <Link
              href="/blog"
              className="btn-soft flex items-center gap-2"
            >
              <OptimizedIcon name="ArrowLeft" className="w-4 h-4" />
              Wszystkie artykuły
            </Link>
          </div>
        </div>

        {/* Floating WhatsApp for Blog - appears after 30% scroll */}
        <div className="fixed bottom-6 right-6 z-50">
          <PerformantWhatsApp
            size="lg"
            categoryColor={post.category}
            showOnScroll={true}
            scrollThreshold={30}
            contextualMessage={`Cześć Julia! Przeczytałam/em artykuł "${post.title}" i chciałabym/chciałbym dowiedzieć się więcej o Twoich retreatach.`}
          />
        </div>
      </div>
      </>
    );
  } catch (error) {
    notFound();
  }
}