#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Funkcja do rekurencyjnego przeszukiwania plików
function findFiles(dir, extensions) {
  let results = [];
  const list = fs.readdirSync(dir);
  
  list.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat && stat.isDirectory()) {
      if (!['node_modules', '.next', '.git'].includes(file)) {
        results = results.concat(findFiles(filePath, extensions));
      }
    } else if (extensions.some(ext => file.endsWith(ext))) {
      results.push(filePath);
    }
  });
  
  return results;
}

// Funkcja do sprawdzenia problemów
function checkStyleIssues(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  const issues = [];
  
  // Sprawdź rounded elements
  const roundedMatches = content.match(/rounded-[a-z]+/g);
  if (roundedMatches) {
    issues.push(`Rounded elements: ${roundedMatches.length}`);
  }
  
  // Sprawdź legacy colors
  const legacyColors = ['bg-shell', 'bg-temple', 'bg-rice', 'bg-mist'];
  legacyColors.forEach(color => {
    if (content.includes(color)) {
      issues.push(`Legacy color: ${color}`);
    }
  });
  
  // Sprawdź hardcoded typography
  const hardcodedTypography = content.match(/<h[1-6]\s+className="[^"]*text-[0-9]+xl[^"]*"/g);
  if (hardcodedTypography) {
    issues.push(`Hardcoded typography: ${hardcodedTypography.length}`);
  }
  
  // Sprawdź font-serif (powinno być font-cormorant)
  if (content.includes('font-serif')) {
    const matches = content.match(/font-serif/g);
    issues.push(`Generic font-serif: ${matches.length}`);
  }
  
  // Sprawdź hardcoded spacing
  const hardcodedSpacing = content.match(/\b(py-20|py-24|px-6|px-8|mb-8|mb-6|gap-8|gap-6)\b/g);
  if (hardcodedSpacing) {
    issues.push(`Hardcoded spacing: ${hardcodedSpacing.length}`);
  }
  
  return issues;
}

// Główna funkcja
function main() {
  const srcDir = path.join(__dirname, 'src');
  const files = findFiles(srcDir, ['.jsx', '.tsx', '.js', '.ts']);
  
  console.log('🔍 FINAL STYLE CONSISTENCY CHECK - BAKASANA');
  console.log('=' .repeat(50));
  
  let totalFiles = 0;
  let filesWithIssues = 0;
  let totalIssues = 0;
  const issueTypes = {};
  
  files.forEach(file => {
    totalFiles++;
    const issues = checkStyleIssues(file);
    
    if (issues.length > 0) {
      filesWithIssues++;
      totalIssues += issues.length;
      
      issues.forEach(issue => {
        const type = issue.split(':')[0];
        issueTypes[type] = (issueTypes[type] || 0) + 1;
      });
    }
  });
  
  // Oblicz score
  const score = Math.max(0, Math.round(((totalFiles - filesWithIssues) / totalFiles) * 100));
  
  console.log(`\n📊 RESULTS:`);
  console.log(`Total files checked: ${totalFiles}`);
  console.log(`Files with issues: ${filesWithIssues}`);
  console.log(`Clean files: ${totalFiles - filesWithIssues}`);
  console.log(`Total issues: ${totalIssues}`);
  console.log(`\n🎯 CONSISTENCY SCORE: ${score}/100`);
  
  if (Object.keys(issueTypes).length > 0) {
    console.log(`\n📋 ISSUE BREAKDOWN:`);
    Object.entries(issueTypes).forEach(([type, count]) => {
      console.log(`  ${type}: ${count} occurrences`);
    });
  }
  
  console.log('\n' + '=' .repeat(50));
  
  if (score >= 95) {
    console.log('🎉 EXCELLENT! Layout consistency is at enterprise level!');
  } else if (score >= 90) {
    console.log('✅ VERY GOOD! Minor issues remain.');
  } else if (score >= 80) {
    console.log('⚠️  GOOD! Some consistency issues need attention.');
  } else {
    console.log('❌ NEEDS WORK! Significant consistency issues found.');
  }
  
  // Zapisz raport
  const report = {
    timestamp: new Date().toISOString(),
    stats: {
      totalFiles,
      filesWithIssues,
      cleanFiles: totalFiles - filesWithIssues,
      totalIssues,
      score
    },
    issueTypes
  };
  
  fs.writeFileSync('final-style-report.json', JSON.stringify(report, null, 2));
  console.log('\n📄 Report saved to: final-style-report.json');
}

main();