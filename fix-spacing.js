#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Mapowanie hardcoded spacing na unified system
const spacingMappings = {
  // Padding mappings
  'py-20': 'py-section',
  'py-24': 'py-section',
  'py-32': 'py-section-lg',
  'px-6': 'px-hero-padding',
  'px-8': 'px-hero-padding',
  'px-4': 'px-container-sm',
  
  // Margin mappings
  'mb-8': 'mb-lg',
  'mb-6': 'mb-md',
  'mb-4': 'mb-sm',
  'mb-12': 'mb-xl',
  'mb-16': 'mb-2xl',
  'mt-8': 'mt-lg',
  'mt-6': 'mt-md',
  'mt-4': 'mt-sm',
  'mt-12': 'mt-xl',
  'mt-16': 'mt-2xl',
  
  // Gap mappings
  'gap-8': 'gap-lg',
  'gap-6': 'gap-md',
  'gap-4': 'gap-sm',
  'gap-12': 'gap-xl',
  'gap-16': 'gap-2xl',
  
  // Space mappings
  'space-y-8': 'space-y-lg',
  'space-y-6': 'space-y-md',
  'space-y-4': 'space-y-sm',
  'space-y-12': 'space-y-xl',
  
  // Specific enterprise spacing
  'py-16': 'py-section-md',
  'py-28': 'py-section-lg',
  'px-hero-padding': 'px-hero-padding', // Already correct
  'container mx-auto': 'container mx-auto', // Already correct
};

// Funkcja do rekurencyjnego przeszukiwania plików
function findFiles(dir, extensions) {
  let results = [];
  const list = fs.readdirSync(dir);
  
  list.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat && stat.isDirectory()) {
      if (!['node_modules', '.next', '.git'].includes(file)) {
        results = results.concat(findFiles(filePath, extensions));
      }
    } else if (extensions.some(ext => file.endsWith(ext))) {
      results.push(filePath);
    }
  });
  
  return results;
}

// Funkcja do naprawy spacing
function fixSpacing(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  let changed = false;
  
  Object.entries(spacingMappings).forEach(([oldSpacing, newSpacing]) => {
    if (oldSpacing !== newSpacing && content.includes(oldSpacing)) {
      // Używamy word boundary, aby nie zastąpić części innych klas
      const regex = new RegExp(`\\b${oldSpacing.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`, 'g');
      if (regex.test(content)) {
        content = content.replace(regex, newSpacing);
        changed = true;
      }
    }
  });
  
  if (changed) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`✅ Fixed spacing in: ${filePath}`);
    return true;
  }
  
  return false;
}

// Główna funkcja
function main() {
  const srcDir = path.join(__dirname, 'src');
  const files = findFiles(srcDir, ['.jsx', '.tsx', '.js', '.ts']);
  
  console.log(`🔍 Found ${files.length} files to check for spacing issues...`);
  
  let fixedCount = 0;
  files.forEach(file => {
    if (fixSpacing(file)) {
      fixedCount++;
    }
  });
  
  console.log(`\n🎉 Fixed spacing in ${fixedCount} files!`);
  console.log('✨ All spacing now uses unified BAKASANA spacing system.');
}

main();