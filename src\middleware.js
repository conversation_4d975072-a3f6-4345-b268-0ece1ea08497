import { NextResponse } from 'next/server';

export function middleware(request) {
  // Pobierz ścieżkę z URL
  const path = request.nextUrl.pathname;

  // Ochrona admin routes - uproszczona wersja bez JWT w middleware
  // JWT verification przeniesione do API routes dla kompatybilności z Edge Runtime
  if (path.startsWith('/admin') && path !== '/admin') {
    // Sprawdź tylko czy jest jakiś token - szczegółowa weryfikacja w API
    const authHeader = request.headers.get('authorization');
    const cookieAuth = request.cookies.get('admin-token');
    
    if (!authHeader && !cookieAuth) {
      return NextResponse.redirect(new URL('/admin', request.url));
    }
  }

  // Dodaj nagłówki bezpieczeństwa
  const response = NextResponse.next();

  // Dodaj nagłówki CORS tylko dla API (z ograniczeniami)
  if (path.startsWith('/api/')) {
    // Bardziej restrykcyjne CORS
    const origin = request.headers.get('origin');
    const allowedOrigins = [
      'https://bakasana-travel.blog',
      'https://www.bakasana-travel.blog',
      'http://localhost:3002' // tylko w development
    ];

    if (allowedOrigins.includes(origin) || process.env.NODE_ENV === 'development') {
      response.headers.set('Access-Control-Allow-Origin', origin || '*');
    }

    response.headers.set('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    response.headers.set('Access-Control-Max-Age', '86400'); // 24 godziny
  }

  // Dodaj dodatkowe nagłówki bezpieczeństwa
  response.headers.set('X-Robots-Tag', 'index, follow');
  response.headers.set('X-DNS-Prefetch-Control', 'on');

  return response;
}

// Matcher dla middleware - ochrona admin i API routes
export const config = {
  matcher: [
    '/api/:path*',
    '/admin/:path*',
    // Dodaj inne ścieżki wymagające ochrony
  ],
};