#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Funkcja do rekurencyjnego przeszukiwania plików
function findFiles(dir, extensions) {
  let results = [];
  const list = fs.readdirSync(dir);
  
  list.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat && stat.isDirectory()) {
      if (!['node_modules', '.next', '.git'].includes(file)) {
        results = results.concat(findFiles(filePath, extensions));
      }
    } else if (extensions.some(ext => file.endsWith(ext))) {
      results.push(filePath);
    }
  });
  
  return results;
}

// Funkcja do sortowania importów
function sortImports(content) {
  const lines = content.split('\n');
  const imports = [];
  const nonImports = [];
  let inImportSection = true;
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    
    if (line.startsWith('import ') || line.startsWith("import'") || line.startsWith('import"')) {
      if (inImportSection) {
        imports.push(line);
      } else {
        nonImports.push(line);
      }
    } else if (line.trim() === '' && inImportSection && imports.length > 0) {
      // Pusta linia po importach - kontynuuj zbieranie importów
      continue;
    } else if (line.trim() !== '' && !line.startsWith('//') && !line.startsWith('/*')) {
      inImportSection = false;
      nonImports.push(line);
    } else {
      if (inImportSection && imports.length === 0) {
        nonImports.push(line);
      } else {
        nonImports.push(line);
      }
    }
  }
  
  if (imports.length === 0) {
    return content; // Brak importów do sortowania
  }
  
  // Kategoryzuj importy
  const reactImports = [];
  const uiImports = [];
  const customImports = [];
  const dataImports = [];
  
  imports.forEach(imp => {
    if (imp.includes('react') || imp.includes('next/')) {
      reactImports.push(imp);
    } else if (imp.includes('@/components/ui/')) {
      uiImports.push(imp);
    } else if (imp.includes('@/components/')) {
      customImports.push(imp);
    } else if (imp.includes('@/data/') || imp.includes('@/lib/') || imp.includes('@/utils/')) {
      dataImports.push(imp);
    } else {
      customImports.push(imp);
    }
  });
  
  // Sortuj każdą kategorię alfabetycznie
  reactImports.sort();
  uiImports.sort();
  customImports.sort();
  dataImports.sort();
  
  // Złóż posortowane importy
  const sortedImports = [];
  
  if (reactImports.length > 0) {
    sortedImports.push(...reactImports);
    if (uiImports.length > 0 || customImports.length > 0 || dataImports.length > 0) {
      sortedImports.push('');
    }
  }
  
  if (uiImports.length > 0) {
    sortedImports.push(...uiImports);
    if (customImports.length > 0 || dataImports.length > 0) {
      sortedImports.push('');
    }
  }
  
  if (customImports.length > 0) {
    sortedImports.push(...customImports);
    if (dataImports.length > 0) {
      sortedImports.push('');
    }
  }
  
  if (dataImports.length > 0) {
    sortedImports.push(...dataImports);
  }
  
  // Znajdź pierwszy nie-import line
  let firstNonImportIndex = 0;
  for (let i = 0; i < nonImports.length; i++) {
    if (nonImports[i].trim() !== '' && !nonImports[i].startsWith('//') && !nonImports[i].startsWith('/*')) {
      firstNonImportIndex = i;
      break;
    }
  }
  
  // Złóż końcowy rezultat
  const result = [
    ...sortedImports,
    '',
    ...nonImports.slice(firstNonImportIndex)
  ];
  
  return result.join('\n');
}

// Funkcja do naprawy import order
function fixImportOrder(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  const originalContent = content;
  
  try {
    content = sortImports(content);
    
    if (content !== originalContent) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ Fixed import order in: ${filePath}`);
      return true;
    }
  } catch (error) {
    console.log(`⚠️  Could not fix imports in: ${filePath} - ${error.message}`);
  }
  
  return false;
}

// Główna funkcja
function main() {
  const srcDir = path.join(__dirname, 'src');
  const files = findFiles(srcDir, ['.jsx', '.tsx', '.js', '.ts']);
  
  console.log(`🔍 Found ${files.length} files to check for import order...`);
  
  let fixedCount = 0;
  files.forEach(file => {
    if (fixImportOrder(file)) {
      fixedCount++;
    }
  });
  
  console.log(`\n🎉 Fixed import order in ${fixedCount} files!`);
  console.log('✨ All imports now follow BAKASANA order: React/Next → UI Components → Custom Components → Data/Utils');
}

main();